// Animations for Growy NET website
document.addEventListener('DOMContentLoaded', function() {
    // Paralax effect for hero section - pouze stín, text zůstáv<PERSON> statický
    const hero = document.querySelector('.hero');
    const heroContent = document.querySelector('.hero-content');

    if (hero && heroContent) {
        // Nastavíme fixní pozici pro hero content
        heroContent.style.transform = 'translate(0px, 0px)';

        // Zachováme efekt stínu sledujícího myš
        document.addEventListener('mousemove', (e) => {
            const x = e.clientX / window.innerWidth;
            const y = e.clientY / window.innerHeight;

            // Pohyb stínu podle pozice myši
            hero.style.setProperty('--mouse-x', x);
            hero.style.setProperty('--mouse-y', y);
        });
    }

    // Scroll animations for feature cards
    const featureCards = document.querySelectorAll('.feature-card');
    const benefitCards = document.querySelectorAll('.benefit-card');
    const blogCards = document.querySelectorAll('.blog-card');

    // Function to check if element is in viewport
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.9 &&
            rect.bottom >= 0
        );
    }

    // Function to add animation class when element is in viewport
    function animateOnScroll() {
        // Animate feature cards
        featureCards.forEach((card, index) => {
            if (isInViewport(card) && !card.classList.contains('animated')) {
                setTimeout(() => {
                    card.classList.add('animated', 'fade-in-up');
                }, index * 150); // Staggered animation
            }
        });

        // Animate benefit cards
        benefitCards.forEach((card, index) => {
            if (isInViewport(card) && !card.classList.contains('animated')) {
                setTimeout(() => {
                    card.classList.add('animated', 'fade-in-up');
                }, index * 100); // Staggered animation
            }
        });

        // Animate blog cards
        blogCards.forEach((card, index) => {
            if (isInViewport(card) && !card.classList.contains('animated')) {
                setTimeout(() => {
                    card.classList.add('animated', 'fade-in-up');
                }, index * 150); // Staggered animation
            }
        });
    }

    // Run on scroll and on page load
    window.addEventListener('scroll', animateOnScroll);
    animateOnScroll(); // Run once on page load
});
