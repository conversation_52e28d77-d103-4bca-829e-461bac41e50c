<?xml version="1.0" encoding="UTF-8"?>
<svg width="120px" height="120px" viewBox="0 0 120 120" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Zálohování</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="securityGradient">
            <stop stop-color="#349968" offset="0%"></stop>
            <stop stop-color="#0D5533" offset="100%"></stop>
        </linearGradient>
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur in="SourceAlpha" stdDeviation="2"></feGaussianBlur>
            <feOffset dx="0" dy="2" result="offsetblur"></feOffset>
            <feComponentTransfer>
                <feFuncA type="linear" slope="0.3"></feFuncA>
            </feComponentTransfer>
            <feMerge>
                <feMergeNode></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Main Server -->
        <rect fill="#E0E0E0" x="25" y="30" width="40" height="60" rx="3" filter="url(#shadow)"></rect>
        
        <!-- Server Units -->
        <rect fill="url(#securityGradient)" x="30" y="35" width="30" height="10" rx="1"></rect>
        <rect fill="#FFFFFF" x="33" y="38" width="4" height="4" rx="1"></rect>
        <circle fill="#FFFFFF" cx="55" cy="40" r="2"></circle>
        
        <rect fill="url(#securityGradient)" x="30" y="50" width="30" height="10" rx="1"></rect>
        <rect fill="#FFFFFF" x="33" y="53" width="4" height="4" rx="1"></rect>
        <circle fill="#FFFFFF" cx="55" cy="55" r="2"></circle>
        
        <rect fill="url(#securityGradient)" x="30" y="65" width="30" height="10" rx="1"></rect>
        <rect fill="#FFFFFF" x="33" y="68" width="4" height="4" rx="1"></rect>
        <circle fill="#FFFFFF" cx="55" cy="70" r="2"></circle>
        
        <rect fill="url(#securityGradient)" x="30" y="80" width="30" height="5" rx="1"></rect>
        
        <!-- Backup Server -->
        <rect fill="#E0E0E0" x="75" y="40" width="30" height="40" rx="3" filter="url(#shadow)"></rect>
        
        <!-- Backup Units -->
        <rect fill="url(#securityGradient)" x="78" y="45" width="24" height="7" rx="1"></rect>
        <rect fill="#FFFFFF" x="80" y="47" width="3" height="3" rx="1"></rect>
        <circle fill="#FFFFFF" cx="97" cy="48.5" r="1.5"></circle>
        
        <rect fill="url(#securityGradient)" x="78" y="55" width="24" height="7" rx="1"></rect>
        <rect fill="#FFFFFF" x="80" y="57" width="3" height="3" rx="1"></rect>
        <circle fill="#FFFFFF" cx="97" cy="58.5" r="1.5"></circle>
        
        <rect fill="url(#securityGradient)" x="78" y="65" width="24" height="7" rx="1"></rect>
        <rect fill="#FFFFFF" x="80" y="67" width="3" height="3" rx="1"></rect>
        <circle fill="#FFFFFF" cx="97" cy="68.5" r="1.5"></circle>
        
        <rect fill="url(#securityGradient)" x="78" y="75" width="24" height="3" rx="1"></rect>
        
        <!-- Data Transfer -->
        <path d="M65,50 C70,50 70,55 75,55" stroke="url(#securityGradient)" stroke-width="2" stroke-linecap="round" stroke-dasharray="3,2"></path>
        <path d="M65,60 C70,60 70,65 75,65" stroke="url(#securityGradient)" stroke-width="2" stroke-linecap="round" stroke-dasharray="3,2"></path>
        <path d="M65,70 C70,70 70,75 75,75" stroke="url(#securityGradient)" stroke-width="2" stroke-linecap="round" stroke-dasharray="3,2"></path>
        
        <!-- Calendar -->
        <rect fill="#FFFFFF" x="15" y="15" width="25" height="30" rx="2" filter="url(#shadow)"></rect>
        <rect fill="url(#securityGradient)" x="15" y="15" width="25" height="8" rx="2"></rect>
        <line x1="20" y1="30" x2="35" y2="30" stroke="#CCCCCC" stroke-width="1"></line>
        <line x1="20" y1="35" x2="35" y2="35" stroke="#CCCCCC" stroke-width="1"></line>
        <text x="27.5" y="27" font-family="Arial" font-size="8" fill="url(#securityGradient)" text-anchor="middle">30</text>
        
        <!-- Clock -->
        <circle fill="#FFFFFF" cx="90" cy="20" r="10" filter="url(#shadow)"></circle>
        <circle fill="url(#securityGradient)" cx="90" cy="20" r="8"></circle>
        <circle fill="#FFFFFF" cx="90" cy="20" r="7"></circle>
        <line x1="90" y1="20" x2="90" y2="15" stroke="url(#securityGradient)" stroke-width="1" stroke-linecap="round"></line>
        <line x1="90" y1="20" x2="94" y2="20" stroke="url(#securityGradient)" stroke-width="1" stroke-linecap="round"></line>
    </g>
</svg>
