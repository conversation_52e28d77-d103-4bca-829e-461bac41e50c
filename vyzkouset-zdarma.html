<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Growy NET - Vyzkoušejte zdarma flexibilní zaměstnaneckou aplikaci pro interní komunikaci a digitalizaci firemních procesů">
    <title>Vyzkoušet zdarma - Growy NET | Flexibilní zaměstnanecká aplikace</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Page Specific Styles */
        .landing-header {
            background-color: var(--light-gray);
            padding-top: 150px;
            padding-bottom: 80px;
            text-align: center;
        }

        .landing-header h1 {
            margin-bottom: 20px;
        }

        .landing-header p {
            max-width: 800px;
            margin: 0 auto 30px;
            font-size: 1.2rem;
        }

        .usps-highlights {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 30px;
        }

        .usp-highlight {
            background-color: var(--white);
            border-radius: 50px;
            padding: 10px 20px;
            display: flex;
            align-items: center;
            box-shadow: var(--shadow);
            transition: var(--transition);
            text-decoration: none;
            color: var(--dark);
            cursor: pointer;
        }

        .usp-highlight:hover {
            transform: translateY(-3px);
            background-color: var(--primary-color);
            color: var(--white);
        }

        .usp-highlight i {
            font-size: 1.2rem;
            margin-right: 10px;
            color: var(--primary-color);
        }

        .usp-highlight:hover i {
            color: var(--white);
        }

        .usp-highlight span {
            font-weight: 500;
        }

        .usp-highlight::after {
            content: "↓";
            margin-left: 8px;
            font-size: 0.9rem;
            opacity: 0.7;
        }

        .landing-container {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            justify-content: space-between;
            margin-top: 50px;
        }

        .landing-form-container {
            flex: 0 0 calc(60% - 15px);
            background-color: var(--white);
            border-radius: 10px;
            box-shadow: var(--shadow);
            padding: 30px;
        }

        .landing-support-container {
            flex: 0 0 calc(40% - 15px);
            background-color: var(--white);
            border-radius: 10px;
            box-shadow: var(--shadow);
            padding: 30px;
            display: flex;
            flex-direction: column;
        }

        .landing-form {
            margin-top: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Poppins', sans-serif;
            font-size: 1rem;
        }

        textarea.form-control {
            min-height: 120px;
            resize: vertical;
        }

        .form-check {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .form-check input {
            margin-right: 10px;
            margin-top: 5px;
        }

        .form-check label {
            font-size: 0.9rem;
        }

        .form-button {
            text-align: center;
        }

        .support-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .support-image {
            flex: 0 0 auto;
            margin-right: 20px;
        }

        .support-image img {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid var(--primary-color);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .support-info {
            flex: 1;
            text-align: left;
        }

        .support-info h3 {
            margin-bottom: 5px;
            color: var(--primary-color);
            font-size: 1.5rem;
        }

        .support-info p {
            color: var(--gray);
            margin-bottom: 0;
            font-weight: 500;
        }

        .support-quote {
            background-color: var(--light-gray);
            border-radius: 10px;
            padding: 20px;
            position: relative;
            margin-bottom: 20px;
            text-align: left;
        }

        .support-quote i {
            color: var(--primary-color);
            font-size: 1.5rem;
            opacity: 0.3;
            position: absolute;
            top: 10px;
            left: 10px;
        }

        .support-quote p {
            margin: 0;
            padding-left: 25px;
            font-style: italic;
            color: var(--dark);
        }

        .support-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 15px 0;
            border-top: 1px solid #eee;
            border-bottom: 1px solid #eee;
        }

        .support-stat {
            text-align: center;
            flex: 1;
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--gray);
        }

        .support-contact {
            margin-top: auto;
        }

        .support-contact a {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px 15px;
            margin-bottom: 10px;
            border-radius: 5px;
            transition: var(--transition);
            text-decoration: none;
        }

        .contact-button {
            background-color: var(--light-gray);
            color: var(--dark);
        }

        .contact-button:hover {
            background-color: var(--primary-color);
            color: var(--white);
        }

        .support-contact i {
            margin-right: 10px;
            font-size: 1.2rem;
        }

        /* Additional support content */
        .support-additional {
            margin-top: 30px;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }

        .support-faq, .support-testimonial {
            margin-bottom: 25px;
        }

        .support-faq h4, .support-testimonial h4 {
            color: var(--primary-color);
            font-size: 1.2rem;
            margin-bottom: 15px;
            position: relative;
            padding-left: 15px;
        }

        .support-faq h4::before, .support-testimonial h4::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 4px;
            background-color: var(--primary-color);
            border-radius: 2px;
        }

        .faq-item {
            background-color: var(--white);
            border-radius: 8px;
            box-shadow: var(--shadow);
            margin-bottom: 15px;
            overflow: hidden;
        }

        .faq-question {
            padding: 15px;
            display: flex;
            align-items: center;
            cursor: pointer;
            font-weight: 500;
            transition: var(--transition);
        }

        .faq-question:hover {
            background-color: rgba(52, 153, 104, 0.05);
        }

        .faq-question i {
            color: var(--primary-color);
            margin-right: 10px;
            font-size: 1.1rem;
        }

        .faq-answer {
            padding: 0 15px 15px 40px;
            color: var(--gray);
            font-size: 0.95rem;
            line-height: 1.5;
        }

        .testimonial-item {
            background-color: var(--white);
            border-radius: 8px;
            box-shadow: var(--shadow);
            padding: 20px;
        }

        .testimonial-text {
            position: relative;
            margin-bottom: 15px;
        }

        .testimonial-text i {
            color: var(--primary-color);
            font-size: 1.2rem;
            opacity: 0.3;
            position: absolute;
            top: 0;
            left: 0;
        }

        .testimonial-text p {
            margin: 0;
            padding-left: 25px;
            font-style: italic;
            color: var(--dark);
            line-height: 1.5;
        }

        .testimonial-author {
            text-align: right;
            font-size: 0.9rem;
            color: var(--gray);
        }

        .usps-section {
            padding: 80px 0;
            background-color: var(--light-gray);
        }

        .usps-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 50px;
        }

        .usp-card {
            background-color: var(--white);
            border-radius: 10px;
            box-shadow: var(--shadow);
            padding: 30px;
            text-align: center;
            transition: var(--transition);
            height: 100%;
        }

        .usp-card:hover {
            transform: translateY(-5px);
        }

        .usp-card.featured {
            border-left: 4px solid var(--primary-color);
            position: relative;
            overflow: hidden;
        }

        .usp-card.featured::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 50px 50px 0;
            border-color: transparent var(--primary-color) transparent transparent;
        }

        @keyframes highlightCard {
            0% { box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1); }
            50% { box-shadow: 0 5px 30px rgba(52, 153, 104, 0.5); }
            100% { box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1); }
        }

        .highlight-card {
            animation: highlightCard 1.5s ease;
        }

        .usp-benefits {
            margin-top: 15px;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }

        .usp-benefits ul {
            padding-left: 20px;
        }

        .usp-benefits li {
            margin-bottom: 8px;
            position: relative;
            list-style-type: none;
        }

        .usp-benefits li::before {
            content: "✓";
            color: var(--primary-color);
            font-weight: bold;
            position: absolute;
            left: -20px;
        }

        .usp-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
        }

        .steps-section {
            padding: 80px 0;
        }

        .steps-container {
            max-width: 800px;
            margin: 50px auto 0;
        }

        .step-item {
            display: flex;
            margin-bottom: 50px;
            position: relative;
        }

        .step-item:last-child {
            margin-bottom: 0;
        }

        .step-item:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 70px;
            left: 35px;
            width: 2px;
            height: calc(100% - 40px);
            background-color: var(--primary-color);
        }

        .step-number {
            flex: 0 0 70px;
            width: 70px;
            height: 70px;
            background-color: var(--primary-color);
            color: var(--white);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 700;
            margin-right: 30px;
            z-index: 1;
        }

        .step-content {
            flex: 1;
        }

        .step-content h3 {
            margin-bottom: 10px;
        }

        @media (max-width: 992px) {
            .landing-form-container, .landing-support-container {
                flex: 0 0 100%;
            }

            .usps-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .usps-highlights {
                gap: 15px;
            }

            .usp-highlight {
                padding: 8px 15px;
                font-size: 0.9rem;
            }
        }

        @media (max-width: 768px) {
            .usps-grid {
                grid-template-columns: 1fr;
            }

            .usps-highlights {
                flex-direction: column;
                align-items: center;
                gap: 10px;
            }

            .usp-highlight {
                width: 80%;
            }

            .step-item {
                flex-direction: column;
                align-items: center;
                text-align: center;
            }

            .step-number {
                margin-right: 0;
                margin-bottom: 20px;
            }

            .step-item:not(:last-child)::after {
                left: 50%;
                top: 70px;
                width: 2px;
                height: 50px;
                transform: translateX(-50%);
            }

            .support-quote p {
                font-size: 0.9rem;
            }

            .support-header {
                flex-direction: column;
                text-align: center;
            }

            .support-image {
                margin-right: 0;
                margin-bottom: 15px;
            }

            .support-info {
                text-align: center;
            }

            .support-stats {
                flex-direction: column;
                gap: 15px;
            }

            .support-stat {
                padding: 10px 0;
                border-bottom: 1px solid #eee;
            }

            .support-stat:last-child {
                border-bottom: none;
            }

            .support-additional {
                margin-top: 20px;
            }

            .faq-question {
                padding: 12px;
                font-size: 0.9rem;
            }

            .faq-answer {
                padding: 0 12px 12px 35px;
                font-size: 0.85rem;
            }

            .testimonial-item {
                padding: 15px;
            }

            .testimonial-text p {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.html" class="logo">
                    <img src="img/logo.png" alt="Growy NET logo">
                </a>
                <div class="menu-toggle" id="mobile-menu">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
                <ul class="nav-menu">
                    <li><a href="index.html" class="nav-link">Domů</a></li>
                    <li><a href="produkt.html" class="nav-link">Produkt</a></li>
                    <li><a href="moduly.html" class="nav-link">Moduly</a></li>
                    <li><a href="pro-koho.html" class="nav-link">Pro koho</a></li>
                    <li><a href="cena.html" class="nav-link">Cena</a></li>
                    <li><a href="reference.html" class="nav-link">Reference</a></li>
                    <li><a href="blog.html" class="nav-link">Blog</a></li>
                    <li><a href="kontakt.html" class="nav-link">Kontakt</a></li>
                </ul>
                <a href="vyzkouset-zdarma.html" class="btn btn-primary">Vyzkoušet zdarma</a>
            </nav>
        </div>
    </header>

    <!-- Landing Header -->
    <section class="landing-header">
        <div class="container">
            <h1>Vyzkoušejte Growy NET zdarma</h1>
            <p>Získejte přístup k demo verzi aplikace Growy NET a objevte, jak může zlepšit interní komunikaci a digitalizovat firemní procesy ve vaší společnosti.</p>

            <!-- USPs Highlights -->
            <div class="usps-highlights">
                <a href="#komunikace" class="usp-highlight">
                    <i class="fas fa-comments"></i>
                    <span>Efektivní komunikace</span>
                </a>
                <a href="#mobilni" class="usp-highlight">
                    <i class="fas fa-mobile-alt"></i>
                    <span>Mobilní přístup</span>
                </a>
                <a href="#modularni" class="usp-highlight">
                    <i class="fas fa-cubes"></i>
                    <span>Modulární řešení</span>
                </a>
                <a href="#bezpecnost" class="usp-highlight">
                    <i class="fas fa-shield-alt"></i>
                    <span>Bezpečnost dat</span>
                </a>
            </div>
        </div>
    </section>

    <!-- Form and Support Section -->
    <section class="section">
        <div class="container">
            <div class="landing-container">
                <div class="landing-form-container">
                    <h2>Vyplňte formulář a získejte demo zdarma</h2>
                    <p>Vyplňte kontaktní formulář a my se Vám ozveme co nejdříve. Rádi Vám poskytneme více informací o našem řešení nebo Vám připravíme demo účet pro vyzkoušení.</p>

                    <form class="landing-form" action="#" method="POST">
                        <div class="form-group">
                            <label for="name">Jméno a příjmení *</label>
                            <input type="text" id="name" name="name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="company">Název společnosti *</label>
                            <input type="text" id="company" name="company" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="position">Vaše pozice</label>
                            <input type="text" id="position" name="position" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="email">E-mail *</label>
                            <input type="email" id="email" name="email" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="phone">Telefon *</label>
                            <input type="tel" id="phone" name="phone" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="employees">Počet zaměstnanců</label>
                            <select id="employees" name="employees" class="form-control">
                                <option value="">Vyberte</option>
                                <option value="1-50">1-50</option>
                                <option value="51-100">51-100</option>
                                <option value="101-200">101-200</option>
                                <option value="201-500">201-500</option>
                                <option value="501+">501+</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="message">Vaše požadavky nebo dotazy</label>
                            <textarea id="message" name="message" class="form-control"></textarea>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" id="gdpr" name="gdpr" required>
                            <label for="gdpr">Souhlasím se <a href="zasady-zpracovani-osobnich-udaju.html" target="_blank">zpracováním osobních údajů</a> *</label>
                        </div>
                        <div class="form-button">
                            <button type="submit" class="btn btn-primary">Získat demo zdarma</button>
                        </div>
                    </form>
                </div>

                <div class="landing-support-container">
                    <div class="support-header">
                        <div class="support-image">
                            <img src="img/Kateřina Čulíková.svg" alt="Kateřina Čulíková">
                        </div>
                        <div class="support-info">
                            <h3>Kateřina Čulíková</h3>
                            <p>Zákaznická podpora</p>
                        </div>
                    </div>

                    <div class="support-quote">
                        <i class="fas fa-quote-left"></i>
                        <p>Jsem tu pro Vás! Ráda Vám pomohu s jakýmkoliv dotazem ohledně Growy NET a provedu Vás celým procesem od prvního kontaktu až po úspěšné nasazení ve Vaší firmě.</p>
                    </div>

                    <div class="support-stats">
                        <div class="support-stat">
                            <div class="stat-number">98%</div>
                            <div class="stat-label">Spokojených zákazníků</div>
                        </div>
                        <div class="support-stat">
                            <div class="stat-number">8-16</div>
                            <div class="stat-label">Pracovní doba</div>
                        </div>
                        <div class="support-stat">
                            <div class="stat-number">5+</div>
                            <div class="stat-label">Let zkušeností</div>
                        </div>
                    </div>

                    <div class="support-contact">
                        <a href="tel:+420725864778" class="contact-button"><i class="fas fa-phone"></i> +420 725 864 778</a>
                        <a href="mailto:<EMAIL>" class="contact-button"><i class="fas fa-envelope"></i> <EMAIL></a>
                        <a href="#" class="btn btn-primary"><i class="fas fa-calendar-alt"></i> Naplánovat hovor</a>
                    </div>

                    <div class="support-additional">
                        <div class="support-faq">
                            <h4>Často kladené dotazy</h4>
                            <div class="faq-item">
                                <div class="faq-question">
                                    <i class="fas fa-question-circle"></i>
                                    <span>Jak dlouho trvá implementace?</span>
                                </div>
                                <div class="faq-answer">
                                    Základní implementace trvá 2-3 týdny. Komplexnější řešení s integrací na vaše systémy může trvat 4-6 týdnů.
                                </div>
                            </div>
                            <div class="faq-item">
                                <div class="faq-question">
                                    <i class="fas fa-question-circle"></i>
                                    <span>Potřebuji IT oddělení pro správu?</span>
                                </div>
                                <div class="faq-answer">
                                    Ne, Growy NET je navržen tak, aby ho mohl spravovat kdokoliv. Poskytujeme kompletní zaškolení a podporu.
                                </div>
                            </div>
                        </div>

                        <div class="support-testimonial">
                            <h4>Co o nás říkají klienti</h4>
                            <div class="testimonial-item">
                                <div class="testimonial-text">
                                    <i class="fas fa-quote-left"></i>
                                    <p>Growy NET nám pomohl zefektivnit interní komunikaci a výrazně snížit počet emailů. Zaměstnanci mají všechny informace na jednom místě.</p>
                                </div>
                                <div class="testimonial-author">
                                    <strong>Jan Novák</strong>, HR manažer, ABC Company
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- USPs Section -->
    <section class="usps-section">
        <div class="container">
            <h2>Proč si vybrat Growy NET?</h2>
            <p class="section-header">Growy NET nabízí jedinečné výhody, které vám pomohou zlepšit interní komunikaci a digitalizovat firemní procesy.</p>

            <div class="usps-grid">
                <div id="komunikace" class="usp-card">
                    <div class="usp-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h3>Efektivní komunikace</h3>
                    <p>Zlepšete interní komunikaci ve vaší firmě díky centralizované platformě, která umožňuje sdílet informace s těmi správnými lidmi ve správný čas.</p>
                    <div class="usp-benefits">
                        <ul>
                            <li>Cílení obsahu na konkrétní skupiny zaměstnanců</li>
                            <li>Okamžité notifikace o důležitých informacích</li>
                            <li>Zpětná vazba pomocí komentářů a reakcí</li>
                        </ul>
                    </div>
                </div>
                <div id="mobilni" class="usp-card">
                    <div class="usp-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>Mobilní přístup</h3>
                    <p>Poskytněte svým zaměstnancům přístup k důležitým informacím kdykoliv a kdekoliv prostřednictvím mobilní aplikace dostupné pro iOS a Android.</p>
                    <div class="usp-benefits">
                        <ul>
                            <li>Aplikace pro iOS a Android</li>
                            <li>Přístup i bez firemního emailu</li>
                            <li>Offline režim pro přístup bez připojení</li>
                        </ul>
                    </div>
                </div>
                <div id="modularni" class="usp-card">
                    <div class="usp-icon">
                        <i class="fas fa-cubes"></i>
                    </div>
                    <h3>Modulární řešení</h3>
                    <p>Přizpůsobte si aplikaci přesně podle vašich potřeb díky modulárnímu řešení, které vám umožní vybrat si pouze ty funkce, které skutečně potřebujete.</p>
                </div>
                <div id="bezpecnost" class="usp-card">
                    <div class="usp-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Bezpečnost dat</h3>
                    <p>Vaše data jsou v bezpečí díky šifrovaným přenosům, pravidelným zálohám a plnému souladu s GDPR.</p>
                </div>
                <div class="usp-card">
                    <div class="usp-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <h3>Integrace se systémy</h3>
                    <p>Propojte Growy NET s vašimi stávajícími systémy díky otevřenému API a získejte jednotnou platformu pro všechny firemní procesy.</p>
                </div>
                <div class="usp-card">
                    <div class="usp-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3>Profesionální podpora</h3>
                    <p>Náš tým je vám k dispozici každý pracovní den od 8:00 do 16:00 a pomůže vám s jakýmkoliv problémem nebo dotazem.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Steps Section -->
    <section class="steps-section">
        <div class="container">
            <h2>Jak začít s Growy NET?</h2>
            <p class="section-header">Začít používat Growy NET je jednoduché. Stačí následovat tyto kroky:</p>

            <div class="steps-container">
                <div class="step-item">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3>Vyplňte formulář</h3>
                        <p>Vyplňte kontaktní formulář na této stránce a my se vám ozveme do 24 hodin.</p>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3>Konzultace s naším specialistou</h3>
                        <p>Náš specialista s vámi probere vaše potřeby a připraví pro vás demo účet přizpůsobený vašim požadavkům.</p>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3>Vyzkoušejte demo verzi</h3>
                        <p>Vyzkoušejte si demo verzi aplikace Growy NET a objevte všechny její funkce a možnosti.</p>
                    </div>
                </div>
                <div class="step-item">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h3>Implementace ve vaší firmě</h3>
                        <p>Pokud budete s demo verzí spokojeni, pomůžeme vám s implementací Growy NET ve vaší firmě a zaškolením vašich zaměstnanců.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
        <div class="container">
            <h2>Připraveni začít?</h2>
            <p>Vyplňte formulář výše a získejte demo verzi Growy NET zdarma.</p>
            <a href="#" class="btn btn-primary" onclick="window.scrollTo({top: 0, behavior: 'smooth'}); return false;">Získat demo zdarma</a>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-top">
                <div class="footer-logo">
                    <img src="img/logo.png" alt="Growy NET logo">
                    <p>Flexibilní zaměstnanecká aplikace</p>
                    <div class="social-links">
                        <a href="https://www.linkedin.com/company/growy-net" target="_blank"><i class="fab fa-linkedin"></i></a>
                        <a href="https://www.facebook.com/growynet.cz/" target="_blank"><i class="fab fa-facebook"></i></a>
                        <a href="https://www.instagram.com/growy_net/" target="_blank"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h3>Stránky</h3>
                    <ul>
                        <li><a href="produkt.html">Produkt</a></li>
                        <li><a href="moduly.html">Moduly</a></li>
                        <li><a href="cena.html">Cena</a></li>
                        <li><a href="kontakt.html">Kontakt</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Kontakt</h3>
                    <p><strong>growy s.r.o.</strong></p>
                    <p>Na Folimance 2155/15, Praha, 120 00</p>
                    <p>+420 702 040 289, <EMAIL></p>
                    <p>IČ: 09477454, DIČ: CZ09477454</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>Copyright © 2025 Growy NET | Powered by Growy</p>
                <div class="footer-legal">
                    <a href="podminky-uzivani.html">Podmínky užívání</a>
                    <a href="zasady-zpracovani-osobnich-udaju.html">Zásady zpracování osobních údajů</a>
                    <a href="zasady-cookies.html">Zásady cookies (EU)</a>
                </div>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script>
        // Smooth scrolling for USP links
        document.addEventListener('DOMContentLoaded', function() {
            const uspLinks = document.querySelectorAll('.usps-highlights a');

            uspLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);

                    if (targetElement) {
                        // Offset for fixed header
                        const headerHeight = document.querySelector('.header').offsetHeight;
                        const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - headerHeight - 20;

                        window.scrollTo({
                            top: targetPosition,
                            behavior: 'smooth'
                        });

                        // Add highlight effect
                        targetElement.classList.add('highlight-card');
                        setTimeout(() => {
                            targetElement.classList.remove('highlight-card');
                        }, 1500);
                    }
                });
            });

            // FAQ toggle functionality
            const faqQuestions = document.querySelectorAll('.faq-question');

            faqQuestions.forEach(question => {
                question.addEventListener('click', function() {
                    const answer = this.nextElementSibling;
                    const isVisible = answer.style.display === 'block';

                    // Hide all answers first
                    document.querySelectorAll('.faq-answer').forEach(ans => {
                        ans.style.display = 'none';
                    });

                    // Toggle current answer
                    if (!isVisible) {
                        answer.style.display = 'block';
                    }
                });
            });

            // Initially hide all FAQ answers
            document.querySelectorAll('.faq-answer').forEach(answer => {
                answer.style.display = 'none';
            });
        });
    </script>
</body>
</html>
