<?xml version="1.0" encoding="UTF-8"?>
<svg width="300px" height="300px" viewBox="0 0 300 300" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>CEO</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="ceoGradient">
            <stop stop-color="#349968" offset="0%"></stop>
            <stop stop-color="#0D5533" offset="100%"></stop>
        </linearGradient>
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur in="SourceAlpha" stdDeviation="3"></feGaussianBlur>
            <feOffset dx="0" dy="3" result="offsetblur"></feOffset>
            <feComponentTransfer>
                <feFuncA type="linear" slope="0.3"></feFuncA>
            </feComponentTransfer>
            <feMerge>
                <feMergeNode></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Background Circle -->
        <circle fill="#F5F5F5" cx="150" cy="150" r="150"></circle>
        
        <!-- CEO Text -->
        <text x="60" y="100" font-family="Arial" font-size="36" font-weight="bold" fill="url(#ceoGradient)">CEO</text>
        <text x="60" y="140" font-family="Arial" font-size="24" fill="#666666">Decision maker</text>
        
        <!-- Crown Icon -->
        <path d="M180,70 L200,90 L220,70 L200,110 L160,110 L140,70 L160,90 L180,70 Z" fill="url(#ceoGradient)" filter="url(#shadow)"></path>
        
        <!-- Person Icon -->
        <circle fill="#FFFFFF" cx="180" cy="140" r="30" filter="url(#shadow)"></circle>
        <circle fill="url(#ceoGradient)" cx="180" cy="130" r="12"></circle>
        <path d="M160,170 C160,155 170,145 180,145 C190,145 200,155 200,170" fill="url(#ceoGradient)"></path>
        
        <!-- Graph/Chart -->
        <rect fill="#FFFFFF" x="80" y="190" width="140" height="80" rx="5" filter="url(#shadow)"></rect>
        
        <!-- Chart Bars -->
        <rect fill="#CCCCCC" x="100" y="230" width="20" height="30" rx="2"></rect>
        <rect fill="#CCCCCC" x="130" y="210" width="20" height="50" rx="2"></rect>
        <rect fill="url(#ceoGradient)" x="160" y="200" width="20" height="60" rx="2"></rect>
        
        <!-- Arrow Up -->
        <path d="M230,220 L250,200 L270,220" stroke="url(#ceoGradient)" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path>
        <line x1="250" y1="200" x2="250" y2="250" stroke="url(#ceoGradient)" stroke-width="4" stroke-linecap="round"></line>
    </g>
</svg>
