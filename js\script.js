// Mobile Menu Toggle
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenu = document.getElementById('mobile-menu');
    const navMenu = document.querySelector('.nav-menu');
    const body = document.body;

    if (mobileMenu) {
        mobileMenu.addEventListener('click', function() {
            mobileMenu.classList.toggle('active');
            navMenu.classList.toggle('active');

            // Prevent body scrolling when menu is open
            if (navMenu.classList.contains('active')) {
                body.style.overflow = 'hidden';
            } else {
                body.style.overflow = '';
            }
        });
    }

    // Close mobile menu when clicking on a nav link
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            mobileMenu.classList.remove('active');
            navMenu.classList.remove('active');
            body.style.overflow = '';
        });
    });

    // Close mobile menu when clicking outside
    document.addEventListener('click', (e) => {
        if (navMenu.classList.contains('active') &&
            !navMenu.contains(e.target) &&
            !mobileMenu.contains(e.target)) {
            mobileMenu.classList.remove('active');
            navMenu.classList.remove('active');
            body.style.overflow = '';
        }
    });

    // Add active class to current page nav link
    const currentLocation = window.location.pathname;
    const navItems = document.querySelectorAll('.nav-link');

    navItems.forEach(item => {
        const href = item.getAttribute('href');
        if (currentLocation.includes(href) && href !== 'index.html') {
            item.classList.add('active');
        } else if (currentLocation === '/' && href === 'index.html') {
            item.classList.add('active');
        }
    });

    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            if (targetId === '#') return;

            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });

    // Header scroll effect
    const header = document.querySelector('.header');
    let lastScrollTop = 0;

    window.addEventListener('scroll', () => {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        if (scrollTop > 100) {
            header.style.padding = '10px 0';
            header.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
        } else {
            header.style.padding = '15px 0';
            header.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.1)';
        }

        lastScrollTop = scrollTop;
    });

    // Typing text animation
    const typingTextElement = document.querySelector('.typing-text');
    if (typingTextElement) {
        const words = ['komunikaci', 'spolupráci', 'produktivitu', 'efektivitu', 'organizaci'];
        let wordIndex = 0;
        let charIndex = 0;
        let isDeleting = false;

        function type() {
            const currentWord = words[wordIndex];

            if (isDeleting) {
                // Deleting text
                typingTextElement.textContent = currentWord.substring(0, charIndex - 1);
                charIndex--;
            } else {
                // Typing text
                typingTextElement.textContent = currentWord.substring(0, charIndex + 1);
                charIndex++;
            }

            // Speed control
            let typeSpeed = isDeleting ? 50 : 100;

            // If word is complete
            if (!isDeleting && charIndex === currentWord.length) {
                // Pause at end of word
                typeSpeed = 1500; // Wait 1.5 seconds
                isDeleting = true;
            } else if (isDeleting && charIndex === 0) {
                isDeleting = false;
                wordIndex = (wordIndex + 1) % words.length;
                typeSpeed = 500; // Wait 0.5 seconds before typing next word
            }

            setTimeout(type, typeSpeed);
        }

        // Start the typing animation
        setTimeout(type, 1000);
    }

    // Interactive phone animation
    const phoneContainer = document.querySelector('.phone-container');
    if (phoneContainer) {
        // 3D rotation effect based on mouse position
        document.addEventListener('mousemove', (e) => {
            if (!phoneContainer.matches(':hover')) return;

            const phoneRect = phoneContainer.getBoundingClientRect();
            const phoneX = phoneRect.left + phoneRect.width / 2;
            const phoneY = phoneRect.top + phoneRect.height / 2;

            // Calculate mouse position relative to the center of the phone
            const mouseX = e.clientX - phoneX;
            const mouseY = e.clientY - phoneY;

            // Calculate rotation angles (limited to small values)
            const rotateY = mouseX * 0.01; // Max ~10 degrees
            const rotateX = -mouseY * 0.01; // Max ~10 degrees

            // Apply the rotation
            phoneContainer.style.transform = `rotateY(${rotateY}deg) rotateX(${rotateX}deg)`;
        });

        // Reset rotation when mouse leaves
        phoneContainer.addEventListener('mouseleave', () => {
            phoneContainer.style.transform = 'rotateY(0deg) rotateX(0deg)';

            // Smooth transition back to original position
            phoneContainer.style.transition = 'transform 0.5s ease';
            setTimeout(() => {
                phoneContainer.style.transition = 'transform 0.1s ease';
            }, 500);
        });

        // Faster transitions when mouse is over the phone
        phoneContainer.addEventListener('mouseenter', () => {
            phoneContainer.style.transition = 'transform 0.1s ease';
        });

        // Interactive notifications
        const notifications = document.querySelectorAll('.notification');

        // Hide notification when clicked
        notifications.forEach(notification => {
            notification.addEventListener('click', () => {
                notification.style.opacity = '0';

                // Show it again after a delay
                setTimeout(() => {
                    notification.style.opacity = '1';
                }, 3000);
            });
        });
    }
});
