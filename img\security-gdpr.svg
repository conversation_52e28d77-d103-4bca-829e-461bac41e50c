<?xml version="1.0" encoding="UTF-8"?>
<svg width="120px" height="120px" viewBox="0 0 120 120" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>GDPR</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="securityGradient">
            <stop stop-color="#349968" offset="0%"></stop>
            <stop stop-color="#0D5533" offset="100%"></stop>
        </linearGradient>
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur in="SourceAlpha" stdDeviation="2"></feGaussianBlur>
            <feOffset dx="0" dy="2" result="offsetblur"></feOffset>
            <feComponentTransfer>
                <feFuncA type="linear" slope="0.3"></feFuncA>
            </feComponentTransfer>
            <feMerge>
                <feMergeNode></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Document -->
        <rect fill="#FFFFFF" x="25" y="20" width="50" height="70" rx="3" filter="url(#shadow)"></rect>
        
        <!-- Document Header -->
        <rect fill="url(#securityGradient)" x="25" y="20" width="50" height="10" rx="3"></rect>
        <text x="50" y="27" font-family="Arial" font-weight="bold" font-size="7" fill="#FFFFFF" text-anchor="middle">GDPR</text>
        
        <!-- Document Content -->
        <line x1="35" y1="40" x2="65" y2="40" stroke="#CCCCCC" stroke-width="1"></line>
        <line x1="35" y1="45" x2="65" y2="45" stroke="#CCCCCC" stroke-width="1"></line>
        <line x1="35" y1="50" x2="65" y2="50" stroke="#CCCCCC" stroke-width="1"></line>
        <line x1="35" y1="55" x2="65" y2="55" stroke="#CCCCCC" stroke-width="1"></line>
        <line x1="35" y1="60" x2="65" y2="60" stroke="#CCCCCC" stroke-width="1"></line>
        <line x1="35" y1="65" x2="55" y2="65" stroke="#CCCCCC" stroke-width="1"></line>
        
        <!-- Checkmark -->
        <circle fill="#FFFFFF" cx="85" cy="40" r="15" filter="url(#shadow)"></circle>
        <circle fill="url(#securityGradient)" cx="85" cy="40" r="12"></circle>
        <path d="M78,40 L83,45 L92,35" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        
        <!-- User Data Protection -->
        <circle fill="#E0E0E0" cx="45" cy="90" r="10" filter="url(#shadow)"></circle>
        <path d="M45,85 C42,85 40,87 40,90 C40,93 42,95 45,95 C48,95 50,93 50,90 C50,87 48,85 45,85 Z" fill="url(#securityGradient)"></path>
        <circle fill="#E0E0E0" cx="45" cy="83" r="3"></circle>
        
        <!-- Shield -->
        <path d="M85,70 L95,75 L95,85 C95,90 85,95 85,95 C85,95 75,90 75,85 L75,75 L85,70 Z" fill="url(#securityGradient)" filter="url(#shadow)"></path>
        <path d="M85,75 L92,78 L92,85 C92,88 85,92 85,92 C85,92 78,88 78,85 L78,78 L85,75 Z" fill="#FFFFFF"></path>
        <text x="85" y="87" font-family="Arial" font-weight="bold" font-size="6" fill="url(#securityGradient)" text-anchor="middle">EU</text>
        
        <!-- EU Stars -->
        <g transform="translate(15, 15)">
            <circle fill="url(#securityGradient)" cx="15" cy="15" r="10" filter="url(#shadow)"></circle>
            <circle fill="#FFFFFF" cx="15" cy="15" r="8"></circle>
            
            <!-- 12 Stars -->
            <path d="M15,7 L16,10 L19,10 L17,12 L18,15 L15,13 L12,15 L13,12 L11,10 L14,10 Z" fill="url(#securityGradient)" transform="scale(0.8) translate(3.75, 3.75)"></path>
        </g>
    </g>
</svg>
