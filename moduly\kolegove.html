<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Growy NET - Modul Kolegov<PERSON> pro správu uživate<PERSON>, rol<PERSON> a oprávnění">
    <title><PERSON><PERSON><PERSON> - Growy NET | Flexibilní zaměstnanecká aplikace</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Page Specific Styles */
        .page-header {
            background-color: var(--light-gray);
            padding-top: 150px;
            padding-bottom: 50px;
            text-align: center;
        }
        
        .module-icon {
            width: 100px;
            height: 100px;
            margin: 0 auto 30px;
        }
        
        .module-section {
            padding: 80px 0;
        }
        
        .module-section:nth-child(even) {
            background-color: var(--light-gray);
        }
        
        .module-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 50px;
            align-items: center;
            margin-top: 50px;
        }
        
        .module-image {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow);
        }
        
        .module-image img {
            width: 100%;
            height: auto;
            display: block;
        }
        
        .module-content h3 {
            margin-bottom: 20px;
            font-size: 1.8rem;
        }
        
        .module-features {
            margin-top: 30px;
        }
        
        .module-features li {
            margin-bottom: 15px;
            position: relative;
            padding-left: 30px;
        }
        
        .module-features li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: var(--primary-color);
            font-weight: 700;
        }
        
        .module-cta {
            text-align: center;
            margin-top: 50px;
        }
        
        .related-modules {
            padding: 80px 0;
            background-color: var(--light-gray);
        }
        
        .related-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 50px;
        }
        
        .related-card {
            background-color: var(--white);
            border-radius: 10px;
            box-shadow: var(--shadow);
            padding: 30px;
            text-align: center;
            transition: var(--transition);
        }
        
        .related-card:hover {
            transform: translateY(-5px);
        }
        
        .related-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto 20px;
        }
        
        @media (max-width: 992px) {
            .module-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .module-image {
                order: 1;
            }
            
            .module-content {
                order: 2;
            }
            
            .related-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .related-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="../index.html" class="logo">
                    <img src="../img/logo.png" alt="Growy NET logo">
                </a>
                <div class="menu-toggle" id="mobile-menu">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
                <ul class="nav-menu">
                    <li><a href="../index.html" class="nav-link">Domů</a></li>
                    <li><a href="../produkt.html" class="nav-link">Produkt</a></li>
                    <li><a href="../moduly.html" class="nav-link active">Moduly</a></li>
                    <li><a href="../pro-koho.html" class="nav-link">Pro koho</a></li>
                    <li><a href="../cena.html" class="nav-link">Cena</a></li>
                    <li><a href="../reference.html" class="nav-link">Reference</a></li>
                    <li><a href="../blog.html" class="nav-link">Blog</a></li>
                    <li><a href="../kontakt.html" class="nav-link">Kontakt</a></li>
                </ul>
                <a href="../vyzkouset-zdarma.html" class="btn btn-primary">Vyzkoušet zdarma</a>
            </nav>
        </div>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="module-icon">
                <img src="../img/module-kolegove.png" alt="Modul Kolegové">
            </div>
            <h1>Modul Kolegové</h1>
            <p>Efektivní správa uživatelských účtů, rolí a oprávnění</p>
        </div>
    </section>

    <!-- Module Overview Section -->
    <section class="module-section">
        <div class="container">
            <div class="module-grid">
                <div class="module-content">
                    <h2>Přehled modulu</h2>
                    <p>Modul Kolegové je základním stavebním kamenem aplikace Growy NET, který umožňuje efektivní správu uživatelských účtů, rolí a oprávnění. Díky tomuto modulu můžete snadno spravovat přístupy zaměstnanců k jednotlivým funkcím aplikace a zajistit, že každý uživatel má přístup pouze k těm informacím, které potřebuje.</p>
                    <p>Modul nabízí přehledný adresář všech zaměstnanců s jejich kontaktními údaji, což usnadňuje interní komunikaci a spolupráci. Zaměstnanci mohou snadno najít kontakt na své kolegy a rychle se s nimi spojit.</p>
                    <div class="module-features">
                        <h3>Klíčové funkce</h3>
                        <ul>
                            <li>Správa uživatelských účtů a profilů</li>
                            <li>Definice rolí a oprávnění</li>
                            <li>Organizační struktura společnosti</li>
                            <li>Adresář zaměstnanců s kontaktními údaji</li>
                            <li>Vyhledávání a filtrování zaměstnanců</li>
                            <li>Možnost přímého kontaktování kolegů</li>
                        </ul>
                    </div>
                </div>
                <div class="module-image">
                    <img src="../img/module-kolegove-screen.jpg" alt="Ukázka modulu Kolegové">
                </div>
            </div>
        </div>
    </section>

    <!-- Module Features Section -->
    <section class="module-section">
        <div class="container">
            <h2>Detailní popis funkcí</h2>
            <div class="module-grid">
                <div class="module-image">
                    <img src="../img/module-kolegove-detail.jpg" alt="Detail modulu Kolegové">
                </div>
                <div class="module-content">
                    <h3>Správa uživatelských účtů</h3>
                    <p>Administrátoři mohou snadno vytvářet, upravovat a deaktivovat uživatelské účty. Každý uživatel má svůj profil s kontaktními údaji, fotografií a dalšími informacemi, které mohou být přizpůsobeny dle potřeb vaší společnosti.</p>
                    
                    <h3>Role a oprávnění</h3>
                    <p>Definujte různé role pro různé skupiny uživatelů a přiřaďte jim odpovídající oprávnění. Můžete vytvořit role jako "Administrátor", "Manažer", "Zaměstnanec" a další, a každé roli přiřadit specifická oprávnění pro přístup k jednotlivým modulům a funkcím aplikace.</p>
                    
                    <h3>Organizační struktura</h3>
                    <p>Vytvořte přehlednou organizační strukturu vaší společnosti s odděleními, týmy a nadřízenými. Zaměstnanci tak mají jasný přehled o struktuře společnosti a svém postavení v ní.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Module Benefits Section -->
    <section class="module-section">
        <div class="container">
            <h2>Výhody modulu Kolegové</h2>
            <div class="module-grid">
                <div class="module-content">
                    <h3>Efektivní správa přístupů</h3>
                    <p>Díky systému rolí a oprávnění můžete snadno řídit, kdo má přístup k jakým informacím a funkcím. To zvyšuje bezpečnost dat a zároveň zajišťuje, že každý zaměstnanec má přístup přesně k těm informacím, které potřebuje pro svou práci.</p>
                    
                    <h3>Lepší komunikace</h3>
                    <p>Adresář zaměstnanců s kontaktními údaji usnadňuje interní komunikaci. Zaměstnanci mohou snadno najít kontakt na své kolegy a rychle se s nimi spojit, což zlepšuje spolupráci a efektivitu práce.</p>
                    
                    <h3>Přehledná organizační struktura</h3>
                    <p>Vizualizace organizační struktury pomáhá novým zaměstnancům rychle se zorientovat ve společnosti a pochopit její fungování. Zároveň poskytuje všem zaměstnancům jasný přehled o tom, kdo je za co zodpovědný.</p>
                </div>
                <div class="module-image">
                    <img src="../img/module-kolegove-benefits.jpg" alt="Výhody modulu Kolegové">
                </div>
            </div>
            <div class="module-cta">
                <a href="../vyzkouset-zdarma.html" class="btn btn-primary">Vyzkoušet modul zdarma</a>
            </div>
        </div>
    </section>

    <!-- Related Modules Section -->
    <section class="related-modules">
        <div class="container">
            <h2>Související moduly</h2>
            <p class="section-header">Tyto moduly dobře fungují společně s modulem Kolegové a rozšiřují jeho možnosti.</p>
            
            <div class="related-grid">
                <a href="chat.html" class="related-card">
                    <div class="related-icon">
                        <img src="../img/module-chat.png" alt="Modul Chat">
                    </div>
                    <h3>Chat</h3>
                    <p>Umožňuje přímou komunikaci mezi kolegy a vytváření skupinových konverzací.</p>
                </a>
                <a href="novinky.html" class="related-card">
                    <div class="related-icon">
                        <img src="../img/module-novinky.png" alt="Modul Novinky">
                    </div>
                    <h3>Novinky</h3>
                    <p>Sdílejte důležité informace a aktuality se všemi zaměstnanci nebo vybranými skupinami.</p>
                </a>
                <a href="faq.html" class="related-card">
                    <div class="related-icon">
                        <img src="../img/module-faq.png" alt="Modul FAQ">
                    </div>
                    <h3>FAQ</h3>
                    <p>Vytvořte databázi často kladených otázek a odpovědí pro rychlou orientaci zaměstnanců.</p>
                </a>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
        <div class="container">
            <h2>Připraveni začít?</h2>
            <p>Vyzkoušejte Growy NET zdarma a objevte, jak může modul Kolegové zlepšit správu uživatelů ve vaší společnosti.</p>
            <a href="../vyzkouset-zdarma.html" class="btn btn-primary">Vyzkoušet zdarma</a>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-top">
                <div class="footer-logo">
                    <img src="../img/logo.png" alt="Growy NET logo">
                    <p>Flexibilní zaměstnanecká aplikace</p>
                    <div class="social-links">
                        <a href="https://www.linkedin.com/company/growy-net" target="_blank"><i class="fab fa-linkedin"></i></a>
                        <a href="https://www.facebook.com/growynet.cz/" target="_blank"><i class="fab fa-facebook"></i></a>
                        <a href="https://www.instagram.com/growy_net/" target="_blank"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h3>Stránky</h3>
                    <ul>
                        <li><a href="../produkt.html">Produkt</a></li>
                        <li><a href="../moduly.html">Moduly</a></li>
                        <li><a href="../cena.html">Cena</a></li>
                        <li><a href="../kontakt.html">Kontakt</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Kontakt</h3>
                    <p><strong>growy s.r.o.</strong></p>
                    <p>Na Folimance 2155/15, Praha, 120 00</p>
                    <p>+420 702 040 289, <EMAIL></p>
                    <p>IČ: 09477454, DIČ: CZ09477454</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>Copyright © 2025 Growy NET | Powered by Growy</p>
                <div class="footer-legal">
                    <a href="../podminky-uzivani.html">Podmínky užívání</a>
                    <a href="../zasady-zpracovani-osobnich-udaju.html">Zásady zpracování osobních údajů</a>
                    <a href="../zasady-cookies.html">Zásady cookies (EU)</a>
                </div>
            </div>
        </div>
    </footer>

    <script src="../js/script.js"></script>
</body>
</html>
