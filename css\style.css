/* Base Styles */
:root {
    --primary-color: #349968;
    --primary-dark: #0D5533;
    --white: #FFFFFF;
    --light-gray: #f5f5f5;
    --gray: #666666;
    --dark: #333333;
    --shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* <PERSON><PERSON><PERSON><PERSON> textu */
::selection {
    background-color: var(--primary-color);
    color: var(--white);
}

::-moz-selection {
    background-color: var(--primary-color);
    color: var(--white);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--dark);
    background-color: var(--white);
}

a {
    text-decoration: none;
    color: inherit;
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

section {
    padding: 80px 0;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 20px;
}

h1 {
    font-size: 3rem;
    position: relative;
    display: inline-block;
    margin-bottom: 30px;
}

h1:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
    border-radius: 2px;
}

h2 {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 40px;
}

h3 {
    font-size: 1.5rem;
}

p {
    margin-bottom: 20px;
}

.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 50px;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    box-shadow: 0 5px 15px rgba(52, 153, 104, 0.3);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-primary:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    z-index: -1;
    transition: opacity 0.3s ease;
    opacity: 0;
}

.btn-primary:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 12px 25px rgba(52, 153, 104, 0.5);
}

.btn-primary:hover:before {
    opacity: 1;
}

.btn-primary:active {
    transform: translateY(-2px) scale(0.98);
    box-shadow: 0 5px 15px rgba(52, 153, 104, 0.4);
    transition: all 0.1s ease;
}

.section-header {
    max-width: 800px;
    margin: 0 auto 60px;
    text-align: center;
}

/* Header & Navigation */
.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 80px; /* Pevná výška pro konzistenci */
    background-color: var(--white);
    box-shadow: var(--shadow);
    z-index: 1000;
    padding: 0;
    display: flex;
    align-items: center;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    width: 100%;
}

.logo {
    display: flex;
    align-items: center;
    height: 100%;
}

.logo img {
    height: 45px;
}

.nav-menu {
    display: flex;
    align-items: center;
}

.nav-link {
    margin: 0 15px;
    font-weight: 500;
    transition: var(--transition);
}

.nav-link:hover, .nav-link.active {
    color: var(--primary-color);
}

.menu-toggle {
    display: none;
    cursor: pointer;
}

.bar {
    display: block;
    width: 25px;
    height: 3px;
    margin: 5px auto;
    background-color: var(--dark);
    transition: var(--transition);
}

/* Hero Section */
.hero {
    padding-top: 120px; /* 80px (výška hlavičky) + 40px (dodatečný prostor) */
    padding-bottom: 80px;
    background-color: var(--light-gray);
    position: relative;
    overflow: hidden;
    --mouse-x: 0.5;
    --mouse-y: 0.5;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(
        circle at calc(var(--mouse-x) * 100%) calc(var(--mouse-y) * 100%),
        rgba(52, 153, 104, 0.08) 0%,
        rgba(13, 85, 51, 0.03) 40%,
        rgba(255, 255, 255, 0) 70%
    );
    z-index: 0;
    pointer-events: none;
    transition: background-position 0.3s ease;
}

.hero-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 1;
}

.hero-text {
    flex: 0.9;
    padding-right: 40px;
    animation: float-slow 6s ease-in-out infinite;
}

@keyframes float-slow {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

.hero-image {
    flex: 1.1;
}

.highlight-text {
    color: var(--primary-color);
    font-weight: 600;
    position: relative;
    display: inline-block;
    transition: all 0.3s ease;
}

.highlight-text:after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary-color);
    transform: scaleX(0);
    transform-origin: bottom right;
    transition: transform 0.3s ease;
}

.highlight-text:hover {
    color: var(--primary-dark);
}

.highlight-text:hover:after {
    transform: scaleX(1);
    transform-origin: bottom left;
}

/* Typing Text Animation */
.typing-text {
    color: var(--primary-color);
    font-weight: 600;
    position: relative;
    display: inline-block;
    border-right: 2px solid var(--primary-color);
    padding-right: 5px;
    animation: cursor-blink 0.7s infinite;
}

@keyframes cursor-blink {
    0%, 100% {
        border-color: transparent;
    }
    50% {
        border-color: var(--primary-color);
    }
}

.hero-image {
    flex: 1;
    text-align: center;
    position: relative;
}

.hero-image img {
    max-height: 800px;
    transition: transform 0.3s ease;
    filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.15));
}

.hero-image img:hover {
    transform: translateY(-5px);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: transparent;
}

.hero-buttons {
    display: flex;
    align-items: center;
    margin-top: 30px;
}

.app-buttons {
    display: flex;
    margin-left: 20px;
}

.app-button {
    margin-right: 10px;
    transition: transform 0.3s ease;
    display: inline-block;
}

.app-button:hover {
    transform: translateY(-3px);
}

.app-button img {
    height: 40px;
    border-radius: 8px;
}

/* Features Section */
.features {
    background-color: var(--white);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
}

.feature-card {
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    background-color: var(--white);
    border: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--dark-green));
    transform: translateY(-100%);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(52, 153, 104, 0.15);
}

.feature-card:hover::before {
    transform: translateY(0);
}

.feature-card h3 {
    font-size: 1.4rem;
    margin-bottom: 15px;
    color: var(--dark);
    transition: color 0.3s ease;
}

.feature-card:hover h3 {
    color: var(--primary-color);
}

.feature-card img {
    margin-bottom: 25px;
    max-height: 100px;
    max-width: 100px;
    transition: transform 0.5s ease;
}

.feature-card:hover img {
    transform: scale(1.05);
}

.feature-card img[src$=".svg"] {
    background-color: transparent;
    padding: 0;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.feature-card:hover img[src$=".svg"] {
    filter: drop-shadow(0 6px 12px rgba(52, 153, 104, 0.2));
}

/* Benefits Section */
.benefits {
    background-color: var(--light-gray);
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.benefit-card {
    background-color: var(--white);
    padding: 30px;
    border-radius: 10px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.benefit-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 0;
    background: linear-gradient(to top, rgba(52, 153, 104, 0.05), transparent);
    transition: height 0.3s ease;
    z-index: -1;
}

.benefit-card:hover {
    transform: translateY(-5px) scale(1.03);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.benefit-card:hover::after {
    height: 100%;
}

.benefit-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.benefits-cta {
    text-align: center;
    margin-top: 50px;
}

/* Testimonial Section */
.testimonial {
    background-color: var(--white);
}

.testimonial-content {
    display: flex;
    align-items: center;
    background-color: var(--light-gray);
    border-radius: 20px;
    padding: 50px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.testimonial-content:hover {
    box-shadow: 0 15px 40px rgba(52, 153, 104, 0.1);
    transform: translateY(-5px);
}

.testimonial-image {
    flex: 0 0 200px;
    margin-right: 40px;
}

.testimonial-image img[src$=".svg"] {
    background-color: #f5f5f5;
    padding: 20px;
    border-radius: 10px;
    max-width: 100%;
}

.testimonial-text {
    flex: 1;
}

.testimonial-text p {
    font-size: 1.1rem;
    font-style: italic;
}

.testimonial-author {
    margin-top: 20px;
}

/* Blog Section */
.blog {
    background-color: var(--light-gray);
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.blog-card {
    background-color: var(--white);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.blog-card:hover {
    transform: translateY(-5px);
}

.blog-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.blog-card:hover .blog-image img {
    transform: scale(1.05);
}

.blog-image img[src$=".svg"] {
    background-color: #f5f5f5;
    padding: 20px;
    object-fit: contain;
}

.blog-image img[src$=".png"],
.blog-image img[src$=".jpg"],
.blog-image img[src$=".jpeg"] {
    object-fit: cover;
}

.blog-content {
    padding: 20px;
}

.blog-date {
    color: var(--gray);
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.blog-link {
    color: var(--primary-color);
    font-weight: 600;
    display: inline-block;
    margin-top: 10px;
}

.blog-link:hover {
    color: var(--primary-dark);
}

/* CTA Section */
.cta {
    background-color: var(--primary-color);
    color: var(--white);
    text-align: center;
    padding: 80px 0;
}

.cta h2 {
    color: var(--white);
    font-size: 2.8rem;
    margin-bottom: 20px;
}

.cta p {
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    font-size: 1.2rem;
}

.cta .btn-primary {
    background-color: transparent;
    color: var(--white);
    font-size: 1.2rem;
    padding: 18px 45px;
    border-radius: 50px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border: 3px solid var(--white);
    transition: all 0.3s ease;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
    position: relative;
    overflow: hidden;
    z-index: 1;
    animation: pulse 2s infinite;
}

.cta .btn-primary:hover {
    background-color: var(--white);
    color: var(--primary-dark);
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
    animation: none;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(255, 255, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

/* Footer */
.footer {
    background-color: var(--dark);
    color: var(--white);
    padding: 60px 0 20px;
}

.footer-top {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 40px;
    margin-bottom: 40px;
}

.footer-logo img {
    height: 45px;
    margin-bottom: 20px;
}

.social-links {
    display: flex;
    margin-top: 20px;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    margin-right: 10px;
    transition: var(--transition);
}

.social-links a:hover {
    background-color: var(--primary-color);
}

.footer-links h3, .footer-contact h3 {
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.footer-links ul li {
    margin-bottom: 10px;
}

.footer-links ul li a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-legal a {
    margin-left: 20px;
    opacity: 0.7;
    transition: var(--transition);
}

.footer-legal a:hover {
    opacity: 1;
}

/* Responsive Styles */
@media (max-width: 992px) {
    h1 {
        font-size: 2.5rem;
    }

    h2 {
        font-size: 2rem;
    }

    .hero-content {
        flex-direction: column;
    }

    .hero-text {
        padding-right: 0;
        margin-bottom: 40px;
    }

    .features-grid, .benefits-grid, .blog-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-top {
        grid-template-columns: 1fr 1fr;
    }
}

@media (max-width: 768px) {
    .header {
        height: 70px; /* Menší výška pro mobilní zařízení */
    }

    .logo img {
        height: 35px; /* Menší logo na mobilních zařízeních */
    }

    .btn-primary {
        padding: 8px 15px;
        font-size: 0.9rem;
    }

    .menu-toggle {
        display: block;
        z-index: 1001;
    }

    .menu-toggle.active .bar:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }

    .menu-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .menu-toggle.active .bar:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 0;
        flex-direction: column;
        background-color: var(--white);
        width: 100%;
        height: 100vh;
        text-align: center;
        transition: var(--transition);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        padding: 80px 0 20px;
        z-index: 1000;
        overflow-y: auto;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-link {
        margin: 15px 0;
        font-size: 1.1rem;
        display: block;
        padding: 10px;
    }

    .nav-link:active {
        background-color: rgba(52, 153, 104, 0.1);
    }

    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.8rem;
    }

    h3 {
        font-size: 1.3rem;
    }

    .section-header {
        margin-bottom: 30px;
    }

    section {
        padding: 50px 0;
    }

    .page-header {
        padding-top: 100px;
        padding-bottom: 30px;
    }

    .features-grid, .benefits-grid, .blog-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .product-card, .feature-card, .benefit-card, .security-card, .integration-card {
        padding: 20px;
    }

    .product-icon {
        font-size: 2rem;
        margin-bottom: 15px;
    }

    .testimonial-content {
        flex-direction: column;
        text-align: center;
    }

    .testimonial-image {
        margin-right: 0;
        margin-bottom: 20px;
    }

    .notification {
        max-width: 150px;
        padding: 6px 10px;
    }

    .notification-title {
        font-size: 0.8rem;
    }

    .notification-text {
        font-size: 0.7rem;
    }

    .cta {
        padding: 40px 0;
    }

    .cta h2 {
        font-size: 1.6rem;
    }

    .footer-top {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }

    .footer-legal {
        margin-top: 20px;
    }

    .footer-legal a {
        display: block;
        margin: 10px 0;
    }

    /* Vylepšení pro dotykové ovládání */
    .btn, .nav-link, .feature-card, .benefit-card, .product-card, .integration-card, .security-card {
        -webkit-tap-highlight-color: transparent;
    }

    .btn:active, .nav-link:active, .feature-card:active, .benefit-card:active, .product-card:active, .integration-card:active, .security-card:active {
        transform: scale(0.98);
    }
}

/* Ještě menší mobilní zařízení */
@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    h1 {
        font-size: 1.8rem;
    }

    h2 {
        font-size: 1.6rem;
    }

    h3 {
        font-size: 1.2rem;
    }

    p {
        font-size: 0.95rem;
    }

    .btn-primary {
        padding: 8px 12px;
        font-size: 0.85rem;
    }

    .page-header {
        padding-top: 90px;
    }

    .product-card, .feature-card, .benefit-card, .security-card, .integration-card {
        padding: 15px;
    }

    .security-card .security-icon {
        height: 80px;
        width: 80px;
    }

    .integration-card {
        height: 120px;
    }

    .integration-logo {
        max-height: 50px;
    }

    .integration-title {
        font-size: 12px;
    }

    .faq-question {
        padding: 15px;
        font-size: 0.9rem;
    }

    .faq-answer {
        font-size: 0.85rem;
    }

    .faq-answer.active {
        padding: 15px;
    }

    .footer-logo img {
        height: 35px;
    }

    .footer-links h3, .footer-contact h3 {
        font-size: 1.1rem;
    }

    .footer-contact p, .footer-links ul li a {
        font-size: 0.9rem;
    }
}

/* Notification Bubbles */
.notification {
    position: absolute;
    background-color: white;
    border-radius: 12px;
    padding: 8px 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    z-index: 3;
    opacity: 0;
    animation: notification-appear 0.5s ease forwards;
    animation-delay: 1s;
    cursor: pointer;
    transition: opacity 0.3s ease, box-shadow 0.3s ease;
    max-width: 200px;
}

.notification:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.notification-1 {
    top: 15%;
    right: 5%;
    animation-delay: 1s;
    z-index: 10;
}

.notification-2 {
    top: 50%;
    right: 5%;
    animation-delay: 2s;
    z-index: 10;
}

.notification-3 {
    bottom: 30%;
    left: 5%;
    animation-delay: 3s;
    z-index: 10;
}

.notification-dot {
    width: 6px;
    height: 6px;
    background-color: var(--primary-color);
    border-radius: 50%;
    margin-right: 8px;
    animation: dot-pulse 2s infinite;
    flex-shrink: 0;
    display: inline-block;
}

.notification-content {
    text-align: left;
}

.notification-title {
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--dark);
}

.notification-text {
    font-size: 0.8rem;
    color: var(--gray);
    white-space: nowrap;
}

/* Phone Screen Overlay */
.phone-screen-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) translateZ(10px);
    width: 70%;
    height: 70%;
    z-index: 2;
    pointer-events: none;
}

.app-icon {
    position: absolute;
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    pointer-events: auto;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.app-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.app-icon-1 {
    top: 20%;
    left: 20%;
    background-color: rgba(52, 153, 104, 0.9);
}

.app-icon-2 {
    top: 20%;
    right: 20%;
    background-color: rgba(13, 85, 51, 0.9);
}

.app-icon-3 {
    bottom: 20%;
    left: 20%;
    background-color: rgba(52, 153, 104, 0.9);
}

.app-icon-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 18px;
    height: 18px;
    background-color: #ff3b30;
    color: white;
    border-radius: 50%;
    font-size: 0.7rem;
    font-weight: bold;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: badge-pulse 2s infinite;
}

@keyframes badge-pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
}

@keyframes notification-appear {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

@keyframes dot-pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.3);
    }
}

/* Animation Classes */
.animated {
    animation-duration: 0.8s;
    animation-fill-mode: both;
}

@keyframes fade-in-up {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation-name: fade-in-up;
}

/* Initial state for animated elements */
.feature-card, .benefit-card, .blog-card {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.5s ease, transform 0.5s ease, box-shadow 0.3s ease;
}

.feature-card.animated, .benefit-card.animated, .blog-card.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Touch device optimizations */
.touch-active {
    transform: scale(0.98) !important;
    transition: transform 0.1s ease !important;
}

/* Disable hover effects on touch devices */
@media (hover: none) {
    .btn:hover, .nav-link:hover, .feature-card:hover, .benefit-card:hover,
    .product-card:hover, .integration-card:hover, .security-card:hover {
        transform: none;
        box-shadow: var(--shadow);
    }

    .integration-card:hover::before, .security-card:hover::before, .feature-card:hover::before {
        transform: scaleX(0);
    }

    .product-image-card:hover .phone-img {
        transform: scale(1.8);
    }

    @media (max-width: 480px) {
        .product-image-card:hover .phone-img {
            transform: scale(1.5);
        }
    }
}
