<?xml version="1.0" encoding="UTF-8"?>
<svg width="120px" height="120px" viewBox="0 0 120 120" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Zabezpečení v aplikaci</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="securityGradient">
            <stop stop-color="#349968" offset="0%"></stop>
            <stop stop-color="#0D5533" offset="100%"></stop>
        </linearGradient>
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur in="SourceAlpha" stdDeviation="2"></feGaussianBlur>
            <feOffset dx="0" dy="2" result="offsetblur"></feOffset>
            <feComponentTransfer>
                <feFuncA type="linear" slope="0.3"></feFuncA>
            </feComponentTransfer>
            <feMerge>
                <feMergeNode></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Mobile Phone -->
        <rect fill="#E0E0E0" x="35" y="15" width="50" height="90" rx="5" filter="url(#shadow)"></rect>
        <rect fill="#FFFFFF" x="40" y="25" width="40" height="65" rx="2"></rect>
        
        <!-- Phone Elements -->
        <circle fill="#CCCCCC" cx="60" cy="20" r="2"></circle>
        <rect fill="#CCCCCC" x="50" y="95" width="20" height="5" rx="2.5"></rect>
        
        <!-- Lock Shield -->
        <path d="M60,35 L75,40 L75,55 C75,65 60,70 60,70 C60,70 45,65 45,55 L45,40 L60,35 Z" fill="url(#securityGradient)" filter="url(#shadow)"></path>
        <path d="M60,40 L70,43.5 L70,55 C70,62 60,65 60,65 C60,65 50,62 50,55 L50,43.5 L60,40 Z" fill="#FFFFFF"></path>
        
        <!-- Lock -->
        <rect fill="url(#securityGradient)" x="55" y="50" width="10" height="10" rx="2"></rect>
        <circle fill="#FFFFFF" cx="60" cy="55" r="2"></circle>
        <rect fill="#FFFFFF" x="59" y="55" width="2" height="3" rx="1"></rect>
        
        <!-- Encryption Lines -->
        <path d="M45,75 L75,75" stroke="url(#securityGradient)" stroke-width="2" stroke-linecap="round" stroke-dasharray="2,2"></path>
        <path d="M45,80 L75,80" stroke="url(#securityGradient)" stroke-width="2" stroke-linecap="round" stroke-dasharray="2,2"></path>
        <path d="M45,85 L65,85" stroke="url(#securityGradient)" stroke-width="2" stroke-linecap="round" stroke-dasharray="2,2"></path>
        
        <!-- Security Elements -->
        <circle fill="#FFFFFF" cx="85" cy="35" r="15" filter="url(#shadow)"></circle>
        <path d="M85,25 L85,35 L95,35" stroke="url(#securityGradient)" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"></path>
        <text x="85" y="40" font-family="Arial" font-size="8" fill="url(#securityGradient)" text-anchor="middle">SSL</text>
        
        <!-- Password Strength -->
        <rect fill="#FFFFFF" x="15" y="55" width="20" height="30" rx="2" filter="url(#shadow)"></rect>
        <rect fill="url(#securityGradient)" x="17" y="60" width="16" height="4" rx="1"></rect>
        <rect fill="url(#securityGradient)" x="17" y="67" width="12" height="4" rx="1"></rect>
        <rect fill="url(#securityGradient)" x="17" y="74" width="8" height="4" rx="1"></rect>
    </g>
</svg>
