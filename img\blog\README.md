# Instrukce pro vytvoření obrázků pro blogové příspěvky Growy NET

Tento dokument obsahuje instrukce pro vytvoření obrázků potřebných pro blogové příspěvky Growy NET. Pro každý obrázek je vytvořen HTML soubor s detailním popisem a specifikacemi.

## Seznam potřebných obrázků

### Obr<PERSON><PERSON><PERSON> autorů
- `author-tomas.jpg` - Portrétní fotografie <PERSON>
- `author-josef.jpg` - Portrétní fotografie Josefa Zemana

### Blog: 5 důvodů proč zaměstnanci nečtou firemní emaily
- `unread-emails.jpg` - Hlavní obrázek zobrazující přeplněnou emailovou schránku
- `email-overload.jpg` - Obr<PERSON><PERSON>k znázorňují<PERSON>í frustrovan<PERSON>ho z<PERSON>ě<PERSON>nance před poč<PERSON>ta<PERSON>em
- `organized-information.jpg` - Obrázek zobrazující přehledně organizované informace v mobilní aplikaci Growy NET

### Blog: Growy NET mluví 5 jazyky!
- `languages-main.jpg` - Hlavní obrázek zobrazující mobilní telefon s aplikací Growy NET a vlajky různých jazyků
- `diverse-team.jpg` - Obrázek zobrazující různorodý mezinárodní tým lidí
- `app-languages.jpg` - Obrázek zobrazující stejnou obrazovku aplikace v různých jazykových verzích
- `languages.jpg` - Menší verze hlavního obrázku pro náhledy

### Blog: COE 25: aneb co nám přinesla letošní účast
- `coe-main.jpg` - Hlavní obrázek zobrazující stánek Growy NET na veletrhu
- `coe-team.jpg` - Obrázek týmu Growy na veletrhu
- `coe-presentation.jpg` - Obrázek zachycující prezentaci Growy NET
- `coe-booth.jpg` - Detailnější záběr na stánek Growy NET
- `coe-expo.jpg` - Menší verze hlavního obrázku pro náhledy

## Postup vytvoření obrázků

1. Pro každý obrázek je vytvořen HTML soubor se stejným názvem (např. `unread-emails.html` pro obrázek `unread-emails.jpg`)
2. Otevřete HTML soubor v prohlížeči pro zobrazení detailního popisu a specifikací obrázku
3. Vytvořte obrázek podle specifikací pomocí grafického editoru nebo služby pro generování obrázků
4. Uložte obrázek ve formátu JPEG s názvem odpovídajícím HTML souboru
5. Optimalizujte obrázek pro web (komprese, správná velikost)
6. Umístěte obrázek do složky `img/blog/`

## Důležité poznámky

- Všechny obrázky by měly být v rozlišení vhodném pro web (optimálně 1200-1600px na šířku pro hlavní obrázky)
- Obrázky by měly být optimalizované pro rychlé načítání
- Dodržujte firemní barvy Growy (#FFFFFF, #349968, #0D5533)
- Pro obrázky autorů použijte existující fotografie Tomáše Vrchoty a Josefa Zemana
- Obrázky by měly být profesionální a vizuálně atraktivní
- Dbejte na konzistentní styl všech obrázků

## Alternativní zdroje obrázků

Pokud nemáte možnost vytvořit vlastní obrázky, můžete použít:

1. Stockové fotografie (např. Shutterstock, Adobe Stock, Unsplash)
2. Generátory obrázků založené na AI (např. DALL-E, Midjourney, Stable Diffusion)
3. Existující obrázky z webu Growy NET (growynet.cz)

## Kontrola kvality

Po vytvoření všech obrázků zkontrolujte:

1. Zda všechny obrázky odpovídají specifikacím
2. Zda jsou všechny obrázky ve správném formátu a rozlišení
3. Zda jsou všechny obrázky optimalizované pro web
4. Zda jsou všechny obrázky vizuálně konzistentní
5. Zda jsou všechny obrázky umístěny ve správné složce (`img/blog/`)

## Testování

Po umístění obrázků do složky `img/blog/` otestujte zobrazení blogových příspěvků v prohlížeči:

1. Otevřete soubor `index.html` v prohlížeči
2. Klikněte na odkaz na blogový příspěvek
3. Zkontrolujte, zda se obrázky správně zobrazují
4. Otestujte responzivitu na různých zařízeních (desktop, tablet, mobil)
