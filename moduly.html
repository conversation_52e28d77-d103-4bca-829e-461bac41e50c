<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Growy NET - Flexibilní zaměstnanecká aplikace pro interní komunikaci a digitalizaci firemních procesů">
    <title>Moduly - Growy NET | Flexibilní zaměstnanecká aplikace</title>
    <link rel="icon" type="image/svg+xml" href="img/favicon.svg">
    <link rel="stylesheet" href="css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Page Specific Styles */
        .page-header {
            background-color: var(--light-gray);
            padding-top: 150px;
            padding-bottom: 50px;
            text-align: center;
        }

        .modules-section {
            padding: 80px 0;
        }

        .modules-section:nth-child(even) {
            background-color: var(--light-gray);
        }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 50px;
        }

        .module-card {
            background-color: var(--white);
            padding: 30px;
            border-radius: 10px;
            box-shadow: var(--shadow);
            text-align: center;
            transition: var(--transition);
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: var(--dark);
        }

        .module-card:hover {
            transform: translateY(-5px);
            color: var(--primary-color);
        }

        .module-icon {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
        }

        .module-icon img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        /* Enterprise Section Styles */
        .enterprise-section {
            background-color: var(--light-gray);
            position: relative;
            overflow: hidden;
        }

        .enterprise-container {
            margin-top: 50px;
            background-color: var(--white);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            transition: all 0.5s ease;
        }

        .enterprise-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(52, 153, 104, 0.15);
        }

        .enterprise-content {
            display: flex;
            padding: 40px;
            gap: 40px;
            align-items: center;
        }

        .enterprise-icon-container {
            position: relative;
            flex: 0 0 200px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .enterprise-icon {
            width: 100%;
            height: auto;
            z-index: 2;
            animation: float 6s ease-in-out infinite;
        }

        .pulse-effect {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background-color: rgba(52, 153, 104, 0.1);
            z-index: 1;
            animation: pulse 2s infinite;
        }

        .enterprise-features {
            flex: 1;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
        }

        .enterprise-feature {
            display: flex;
            gap: 15px;
            transition: all 0.3s ease;
            padding: 15px;
            border-radius: 10px;
        }

        .enterprise-feature:hover {
            background-color: rgba(52, 153, 104, 0.05);
            transform: translateY(-3px);
        }

        .feature-icon {
            flex: 0 0 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border-radius: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--white);
            font-size: 1.5rem;
            box-shadow: 0 5px 15px rgba(52, 153, 104, 0.2);
        }

        .feature-text {
            flex: 1;
        }

        .feature-text h3 {
            font-size: 1.2rem;
            margin-bottom: 5px;
            color: var(--dark);
        }

        .feature-text p {
            font-size: 0.9rem;
            color: var(--gray);
            margin-bottom: 0;
        }

        .enterprise-cta {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            padding: 30px;
            text-align: center;
            color: var(--white);
        }

        .enterprise-cta p {
            font-size: 1.2rem;
            margin-bottom: 20px;
        }

        .enterprise-cta .btn-primary {
            background-color: transparent;
            color: var(--white);
            border: 3px solid var(--white);
            font-weight: 700;
            letter-spacing: 1px;
            text-transform: uppercase;
            position: relative;
            z-index: 1;
        }

        .enterprise-cta .btn-primary::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: inherit;
            z-index: -1;
            animation: pulse 2s infinite;
        }

        .enterprise-cta .btn-primary:hover {
            background-color: var(--white);
            color: var(--primary-dark);
            transform: translateY(-5px);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
        }

        .enterprise-cta .btn-primary:hover::after {
            animation: none;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-15px);
            }
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
            }
            70% {
                box-shadow: 0 0 0 15px rgba(255, 255, 255, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
            }
        }

        @media (max-width: 992px) {
            .modules-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .enterprise-content {
                flex-direction: column;
                text-align: center;
            }

            .enterprise-features {
                grid-template-columns: repeat(2, 1fr);
            }

            .enterprise-feature {
                flex-direction: column;
                align-items: center;
                text-align: center;
            }
        }

        @media (max-width: 768px) {
            .modules-grid {
                grid-template-columns: 1fr;
            }

            .enterprise-features {
                grid-template-columns: 1fr;
            }
        }

        /* Úprava CTA tlačítka */
        .cta .btn-primary {
            background-color: transparent;
            color: var(--white);
            border: 3px solid var(--white);
            font-weight: 700;
            letter-spacing: 1px;
            text-transform: uppercase;
            position: relative;
            z-index: 1;
            animation: none;
        }

        .cta .btn-primary::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: inherit;
            z-index: -1;
            animation: pulse 2s infinite;
        }

        .cta .btn-primary:hover {
            background-color: var(--white);
            color: var(--primary-dark);
            transform: translateY(-5px);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
        }

        .cta .btn-primary:hover::after {
            animation: none;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.html" class="logo">
                    <img src="img/logo.png" alt="Growy NET logo">
                </a>
                <div class="menu-toggle" id="mobile-menu">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
                <ul class="nav-menu">
                    <li><a href="index.html" class="nav-link">Domů</a></li>
                    <li><a href="produkt.html" class="nav-link">Produkt</a></li>
                    <li><a href="moduly.html" class="nav-link active">Moduly</a></li>
                    <li><a href="pro-koho.html" class="nav-link">Pro koho</a></li>
                    <li><a href="cena.html" class="nav-link">Cena</a></li>
                    <li><a href="reference.html" class="nav-link">Reference</a></li>
                    <li><a href="blog.html" class="nav-link">Blog</a></li>
                    <li><a href="kontakt.html" class="nav-link">Kontakt</a></li>
                </ul>
                <a href="vyzkouset-zdarma.html" class="btn btn-primary">Vyzkoušet zdarma</a>
            </nav>
        </div>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1>Flexibilní stavebnicové řešení</h1>
            <p>Začněte se základními moduly v balíčku Start a přidávejte další moduly z naší nabídky Plus nebo přidávejte moduly na míru dle Vašich potřeb.</p>
        </div>
    </section>

    <!-- Start Modules Section -->
    <section class="modules-section">
        <div class="container">
            <h2>Moduly Start</h2>
            <p class="section-header">Základní balíček modulů, který zjednoduší komunikaci a spolupráci zaměstnanců v každé firmě.</p>

            <div class="modules-grid">
                <a href="moduly/kolegove.html" class="module-card">
                    <div class="module-icon">
                        <img src="img/moduly/icon-7.png" alt="Modul Kolegové">
                    </div>
                    <h3>Kolegové</h3>
                    <p>Snadno spravujte účty kolegů díky nastavení uživatelských práv a rolí.</p>
                </a>
                <a href="moduly/chat.html" class="module-card">
                    <div class="module-icon">
                        <img src="img/moduly/Group-13-3.png" alt="Modul Chat">
                    </div>
                    <h3>Chat</h3>
                    <p>Zůstaňte ve spojení s celým týmem a nezmeškejte žádné důležité informace.</p>
                </a>
                <a href="moduly/novinky.html" class="module-card">
                    <div class="module-icon">
                        <img src="img/moduly/Group-16.png" alt="Modul Novinky">
                    </div>
                    <h3>Novinky</h3>
                    <p>Buďte vždy v obraze! Sdílejte nejnovější informace a aktuality přímo s vašimi kolegy.</p>
                </a>
                <div class="module-card">
                    <div class="module-icon">
                        <img src="img/moduly/icon-1.png" alt="Modul Události">
                    </div>
                    <h3>Události</h3>
                    <p>Organizujte a plánujte firemní události včetně přehledu účastníků.</p>
                </div>
                <div class="module-card">
                    <div class="module-icon">
                        <img src="img/moduly/icon-1.png" alt="Modul Ankety">
                    </div>
                    <h3>Ankety</h3>
                    <p>Získejte zpětnou vazbu od týmu a vytvářejte ankety pro vyšší angažovanost zaměstnanců.</p>
                </div>
                <div class="module-card">
                    <div class="module-icon">
                        <img src="img/moduly/icon-2.png" alt="Modul Důležitá sdělení">
                    </div>
                    <h3>Důležitá sdělení</h3>
                    <p>Informujte zaměstnance o klíčových informacích rychle a efektivně – přímo z mobilní aplikace.</p>
                </div>
                <div class="module-card">
                    <div class="module-icon">
                        <img src="img/moduly/icon-3.png" alt="Modul Galerie">
                    </div>
                    <h3>Galerie</h3>
                    <p>Uchovejte své vzpomínky na firemní události na jednom místě.</p>
                </div>
                <div class="module-card">
                    <div class="module-icon">
                        <img src="img/moduly/icon-2.png" alt="Modul Rozcestník">
                    </div>
                    <h3>Rozcestník</h3>
                    <p>Nasměrujte rychle své zaměstnance na všechna důležitá místa.</p>
                </div>
                <div class="module-card">
                    <div class="module-icon">
                        <img src="img/moduly/iconFAQ.png" alt="Modul FAQ">
                    </div>
                    <h3>FAQ</h3>
                    <p>Prozkoumejte naši databázi dotazů a rychle najděte, co potřebujete.</p>
                </div>
                <div class="module-card">
                    <div class="module-icon">
                        <img src="img/moduly/icon-8.png" alt="Modul O firmě">
                    </div>
                    <h3>O firmě</h3>
                    <p>Získejte přehled o historii a firemní kultuře Vaší nové firmy.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Plus Modules Section -->
    <section class="modules-section">
        <div class="container">
            <h2>Moduly Plus</h2>
            <p class="section-header">Základními moduly to nekončí. Přidávejte flexibilně další moduly z balíčku Plus podle Vašich aktuálních potřeb. Neustále pracujeme na nových modulech.</p>

            <div class="modules-grid">
                <div class="module-card">
                    <div class="module-icon">
                        <img src="img/moduly/icon-5.png" alt="Modul Firemní časopis">
                    </div>
                    <h3>Firemní časopis</h3>
                    <p>Informujte zaměstnance o nejnovějších aktualitách a udržujte je v obraze.</p>
                </div>
                <div class="module-card">
                    <div class="module-icon">
                        <img src="img/moduly/Group-18.png" alt="Modul Směrnice">
                    </div>
                    <h3>Směrnice</h3>
                    <p>Pomozte zaměstnancům zůstat v obraze o důležitých směrnicích a firemních pravidlech.</p>
                </div>
                <div class="module-card">
                    <div class="module-icon">
                        <img src="img/moduly/iconsd-1.png" alt="Modul Schránka důvěry">
                    </div>
                    <h3>Schránka důvěry</h3>
                    <p>Zajistěte bezpečné prostředí pomocí naší schránky důvěry, která chrání anonymitu zaměstnanců a řeší jejich problémy.</p>
                </div>
                <div class="module-card">
                    <div class="module-icon">
                        <img src="img/moduly/icon-6.png" alt="Modul Žádosti">
                    </div>
                    <h3>Žádosti</h3>
                    <p>Zefektivněte proces schvalování žádostí ve firmě. Vše v digitální podobě.</p>
                </div>
                <div class="module-card">
                    <div class="module-icon">
                        <img src="img/moduly/Group-14.png" alt="Modul Hlášení incidentů">
                    </div>
                    <h3>Hlášení incidentů</h3>
                    <p>Pomozte udržovat bezpečné pracovní prostředí hlášením problémů a incidentů nadřízeným.</p>
                </div>
                <div class="module-card">
                    <div class="module-icon">
                        <img src="img/moduly/icon.png" alt="Modul Nápady a zlepšovací návrhy">
                    </div>
                    <h3>Nápady a zlepšovací návrhy</h3>
                    <p>Umožněte svým zaměstnancům přinášet kreativní nápady a zlepšovat tak firemní prostředí.</p>
                </div>
                <div class="module-card">
                    <div class="module-icon">
                        <img src="img/moduly/Group-17.png" alt="Modul Onboarding">
                    </div>
                    <h3>Onboarding</h3>
                    <p>Usnadněte novým zaměstnancům jejich první kroky ve firmě. Nábor nováčků nebyl nikdy jednodušší.</p>
                </div>
                <div class="module-card">
                    <div class="module-icon">
                        <img src="img/moduly/icon-9.png" alt="Modul Projekty">
                    </div>
                    <h3>Projekty</h3>
                    <p>Udržujte si přehled o aktuálních i historických projektech včetně zapojených kolegů, milníků a použitých technologií.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Enterprise Modules Section -->
    <section class="modules-section enterprise-section">
        <div class="container">
            <h2>Moduly na míru Enterprise</h2>
            <p class="section-header">Přizpůsobíme Vám aplikaci na míru a vyvineme další moduly dle Vašich představ.</p>

            <div class="enterprise-container">
                <div class="enterprise-content">
                    <div class="enterprise-icon-container">
                        <img src="img/enterprise-icon.svg" alt="Enterprise řešení" class="enterprise-icon">
                        <div class="pulse-effect"></div>
                    </div>
                    <div class="enterprise-features">
                        <div class="enterprise-feature">
                            <div class="feature-icon">
                                <i class="fas fa-code"></i>
                            </div>
                            <div class="feature-text">
                                <h3>Vývoj na míru</h3>
                                <p>Vytvoříme moduly přesně podle vašich požadavků a firemních procesů.</p>
                            </div>
                        </div>
                        <div class="enterprise-feature">
                            <div class="feature-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="feature-text">
                                <h3>Integrace se systémy</h3>
                                <p>Propojíme aplikaci s vašimi stávajícími firemními systémy a databázemi.</p>
                            </div>
                        </div>
                        <div class="enterprise-feature">
                            <div class="feature-icon">
                                <i class="fas fa-paint-brush"></i>
                            </div>
                            <div class="feature-text">
                                <h3>Vlastní design</h3>
                                <p>Přizpůsobíme vzhled aplikace vašim firemním barvám a vizuální identitě.</p>
                            </div>
                        </div>
                        <div class="enterprise-feature">
                            <div class="feature-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="feature-text">
                                <h3>Analytické nástroje</h3>
                                <p>Získejte pokročilé přehledy a statistiky o využívání aplikace.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="enterprise-cta">
                    <p>Máte specifické požadavky nebo potřebujete řešení na míru?</p>
                    <a href="kontakt.html" class="btn btn-primary">Kontaktujte nás</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Dále od nás získáte Section -->
    <section class="modules-section">
        <div class="container">
            <h2>Dále od nás získáte</h2>
            <div class="benefits-grid">
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3>Technickou podporu</h3>
                    <p>Naši specialisté jsou připraveni vám pomoci s jakýmkoliv problémem nebo dotazem.</p>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <h3>Pravidelné aktualizace</h3>
                    <p>Neustále vylepšujeme naši aplikaci a přidáváme nové funkce podle potřeb našich klientů.</p>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>Školení zaměstnanců</h3>
                    <p>Pomůžeme vám s implementací a zaškolením vašich zaměstnanců pro maximální využití aplikace.</p>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Bezpečnost dat</h3>
                    <p>Vaše data jsou u nás v bezpečí díky moderním technologiím a pravidelným bezpečnostním auditům.</p>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h3>Integrace se systémy</h3>
                    <p>Propojíme Growy NET s vašimi stávajícími firemními systémy pro maximální efektivitu.</p>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>Analytické nástroje</h3>
                    <p>Získejte přehled o využívání aplikace a měřte její přínos pro vaši firmu.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonial Section -->
    <section class="testimonial">
        <div class="container">
            <div class="testimonial-content">
                <div class="testimonial-image">
                    <img src="img/drbna-logo.svg" alt="Drbna logo">
                </div>
                <div class="testimonial-text">
                    <p>"S růstem našeho týmu v regionech po celé České republice jsme potřebovali najít nástroj, který by usnadnil komunikaci a předávání informací mezi jednotlivými týmy. Tímto nástrojem se stala aplikace Growy NET, kterou má každý zaměstnanec nainstalovanou ve svém telefonu. Díky ní mají všichni přístup k aktuálním informacím a mohou využívat užitečné moduly, které jim zjednodušují práci."</p>
                    <div class="testimonial-author">
                        <p><strong>Matěj Matoušek</strong>, jednatel</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
        <div class="container">
            <h2>Zaujalo Vás naše řešení?</h2>
            <p>Kontaktujte nás a vyzkoušejte aplikaci na vlastní kůži.</p>
            <a href="vyzkouset-zdarma.html" class="btn btn-primary">Vyzkoušet zdarma</a>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-top">
                <div class="footer-logo">
                    <img src="img/logo.png" alt="Growy NET logo">
                    <p>Flexibilní zaměstnanecká aplikace</p>
                    <div class="social-links">
                        <a href="https://www.linkedin.com/company/growy-net" target="_blank"><i class="fab fa-linkedin"></i></a>
                        <a href="https://www.facebook.com/growynet.cz/" target="_blank"><i class="fab fa-facebook"></i></a>
                        <a href="https://www.instagram.com/growy_net/" target="_blank"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h3>Stránky</h3>
                    <ul>
                        <li><a href="produkt.html">Produkt</a></li>
                        <li><a href="moduly.html">Moduly</a></li>
                        <li><a href="cena.html">Cena</a></li>
                        <li><a href="kontakt.html">Kontakt</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Kontakt</h3>
                    <p><strong>growy s.r.o.</strong></p>
                    <p>Na Folimance 2155/15, Praha, 120 00</p>
                    <p>+420 702 040 289, <EMAIL></p>
                    <p>IČ: 09477454, DIČ: CZ09477454</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>Copyright © 2025 Growy NET | Powered by Growy</p>
                <div class="footer-legal">
                    <a href="podminky-uzivani.html">Podmínky užívání</a>
                    <a href="zasady-zpracovani-osobnich-udaju.html">Zásady zpracování osobních údajů</a>
                    <a href="zasady-cookies.html">Zásady cookies (EU)</a>
                </div>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script src="js/animations.js"></script>
    <script>
        // Enterprise Section Animations
        document.addEventListener('DOMContentLoaded', function() {
            const enterpriseContainer = document.querySelector('.enterprise-container');
            const enterpriseFeatures = document.querySelectorAll('.enterprise-feature');

            // Animate features on scroll
            function isInViewport(element) {
                const rect = element.getBoundingClientRect();
                return (
                    rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8 &&
                    rect.bottom >= 0
                );
            }

            function animateEnterpriseSection() {
                if (isInViewport(enterpriseContainer)) {
                    enterpriseContainer.style.opacity = '1';
                    enterpriseContainer.style.transform = 'translateY(0)';

                    // Animate features with delay
                    enterpriseFeatures.forEach((feature, index) => {
                        setTimeout(() => {
                            feature.style.opacity = '1';
                            feature.style.transform = 'translateY(0)';
                        }, 200 * index);
                    });
                }
            }

            // Set initial state
            enterpriseContainer.style.opacity = '0';
            enterpriseContainer.style.transform = 'translateY(30px)';
            enterpriseContainer.style.transition = 'all 0.8s ease';

            enterpriseFeatures.forEach(feature => {
                feature.style.opacity = '0';
                feature.style.transform = 'translateY(20px)';
                feature.style.transition = 'all 0.5s ease';
            });

            // Run animation on scroll and on load
            window.addEventListener('scroll', animateEnterpriseSection);
            animateEnterpriseSection();

            // Add hover effect to icon
            const enterpriseIcon = document.querySelector('.enterprise-icon');
            const pulseEffect = document.querySelector('.pulse-effect');

            enterpriseIcon.addEventListener('mouseenter', () => {
                pulseEffect.style.animation = 'pulse 1s infinite';
                enterpriseIcon.style.transform = 'scale(1.05)';
            });

            enterpriseIcon.addEventListener('mouseleave', () => {
                pulseEffect.style.animation = 'pulse 2s infinite';
                enterpriseIcon.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>
