<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="200px" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Enterprise Icon</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="enterpriseGradient">
            <stop stop-color="#349968" offset="0%"></stop>
            <stop stop-color="#0D5533" offset="100%"></stop>
        </linearGradient>
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur in="SourceAlpha" stdDeviation="3"></feGaussianBlur>
            <feOffset dx="0" dy="2" result="offsetblur"></feOffset>
            <feComponentTransfer>
                <feFuncA type="linear" slope="0.3"></feFuncA>
            </feComponentTransfer>
            <feMerge>
                <feMergeNode></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Base Building -->
        <rect fill="url(#enterpriseGradient)" filter="url(#shadow)" x="40" y="80" width="120" height="80" rx="4"></rect>
        
        <!-- Windows -->
        <rect fill="#FFFFFF" x="50" y="90" width="15" height="15" rx="2"></rect>
        <rect fill="#FFFFFF" x="75" y="90" width="15" height="15" rx="2"></rect>
        <rect fill="#FFFFFF" x="100" y="90" width="15" height="15" rx="2"></rect>
        <rect fill="#FFFFFF" x="125" y="90" width="15" height="15" rx="2"></rect>
        
        <rect fill="#FFFFFF" x="50" y="115" width="15" height="15" rx="2"></rect>
        <rect fill="#FFFFFF" x="75" y="115" width="15" height="15" rx="2"></rect>
        <rect fill="#FFFFFF" x="100" y="115" width="15" height="15" rx="2"></rect>
        <rect fill="#FFFFFF" x="125" y="115" width="15" height="15" rx="2"></rect>
        
        <rect fill="#FFFFFF" x="50" y="140" width="15" height="15" rx="2"></rect>
        <rect fill="#FFFFFF" x="75" y="140" width="15" height="15" rx="2"></rect>
        <rect fill="#FFFFFF" x="100" y="140" width="15" height="15" rx="2"></rect>
        <rect fill="#FFFFFF" x="125" y="140" width="15" height="15" rx="2"></rect>
        
        <!-- Top Tower -->
        <rect fill="url(#enterpriseGradient)" filter="url(#shadow)" x="70" y="40" width="60" height="40" rx="4"></rect>
        
        <!-- Tower Windows -->
        <rect fill="#FFFFFF" x="80" y="50" width="10" height="10" rx="1"></rect>
        <rect fill="#FFFFFF" x="100" y="50" width="10" height="10" rx="1"></rect>
        <rect fill="#FFFFFF" x="80" y="65" width="10" height="10" rx="1"></rect>
        <rect fill="#FFFFFF" x="100" y="65" width="10" height="10" rx="1"></rect>
        
        <!-- Gear Elements -->
        <circle fill="#349968" cx="160" cy="50" r="20" filter="url(#shadow)"></circle>
        <circle fill="#FFFFFF" cx="160" cy="50" r="8"></circle>
        
        <!-- Gear Teeth -->
        <rect fill="#349968" x="155" y="25" width="10" height="8" rx="2"></rect>
        <rect fill="#349968" x="155" y="67" width="10" height="8" rx="2"></rect>
        <rect fill="#349968" x="135" y="45" width="8" height="10" rx="2"></rect>
        <rect fill="#349968" x="177" y="45" width="8" height="10" rx="2"></rect>
        
        <!-- Diagonal Teeth -->
        <rect fill="#349968" transform="translate(143, 33) rotate(45) translate(-143, -33)" x="138" y="29" width="10" height="8" rx="2"></rect>
        <rect fill="#349968" transform="translate(177, 33) rotate(135) translate(-177, -33)" x="172" y="29" width="10" height="8" rx="2"></rect>
        <rect fill="#349968" transform="translate(143, 67) rotate(135) translate(-143, -67)" x="138" y="63" width="10" height="8" rx="2"></rect>
        <rect fill="#349968" transform="translate(177, 67) rotate(45) translate(-177, -67)" x="172" y="63" width="10" height="8" rx="2"></rect>
        
        <!-- Document Elements -->
        <rect fill="#FFFFFF" filter="url(#shadow)" x="25" y="60" width="30" height="40" rx="2"></rect>
        <rect fill="#349968" x="30" y="70" width="20" height="2" rx="1"></rect>
        <rect fill="#349968" x="30" y="75" width="20" height="2" rx="1"></rect>
        <rect fill="#349968" x="30" y="80" width="15" height="2" rx="1"></rect>
        <rect fill="#349968" x="30" y="85" width="20" height="2" rx="1"></rect>
        <rect fill="#349968" x="30" y="90" width="10" height="2" rx="1"></rect>
    </g>
</svg>
