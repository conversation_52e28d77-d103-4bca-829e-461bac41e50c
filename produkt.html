<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="description" content="Growy NET - Flexibilní zaměstnanecká aplikace pro interní komunikaci a digitalizaci firemních procesů">
    <meta name="theme-color" content="#349968">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>Produkt - Growy NET | Flexibilní zaměstnanecká aplikace</title>
    <link rel="icon" type="image/svg+xml" href="img/favicon.svg">
    <link rel="stylesheet" href="css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Page Specific Styles */
        .page-header {
            background-color: var(--light-gray);
            padding-top: 150px;
            padding-bottom: 50px;
            text-align: center;
        }

        .product-section {
            padding: 80px 0;
        }

        .product-section:nth-child(even) {
            background-color: var(--light-gray);
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 50px;
        }

        .product-card {
            background-color: var(--white);
            padding: 30px;
            border-radius: 10px;
            box-shadow: var(--shadow);
            text-align: center;
            transition: var(--transition);
        }

        .product-card:hover {
            transform: translateY(-5px);
        }

        .product-icon {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 20px;
        }

        .product-image-container {
            display: flex;
            justify-content: space-around;
            margin-top: 50px;
            margin-bottom: 30px; /* Přidáme mezeru pod kontejnerem */
            flex-wrap: wrap;
        }

        .product-image-card {
            flex: 0 0 30%;
            text-align: center;
            background: transparent;
            box-shadow: none;
            padding: 0;
        }

        .product-image-card .phone-wrapper {
            position: relative;
            display: inline-block;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .product-image-card:hover .phone-wrapper {
            transform: translateY(-5px);
        }

        .product-image-card .phone-img {
            height: auto;
            width: auto;
            max-width: 100%;
            max-height: none;
            transform: scale(2.2); /* Zachováme velikost telefonů */
            transition: all 0.3s ease;
            z-index: 2;
            position: relative;
            box-shadow: none; /* Odstraníme stín */
            background: transparent; /* Průhledné pozadí */
        }

        .product-image-card:hover .phone-img {
            transform: scale(2.25); /* Jen mírně zvětšíme při najetí myší */
            filter: drop-shadow(0 8px 15px rgba(52, 153, 104, 0.15));
        }

        /* Mobilní optimalizace pro telefony */
        @media (max-width: 768px) {
            .product-image-card .phone-img {
                transform: scale(1.8); /* Menší na mobilech, ale stále dost velké */
            }

            .product-image-card:hover .phone-img {
                transform: scale(1.85); /* Menší zvětšení na mobilech */
            }

            .product-image-card h3 {
                margin-top: 90px; /* Menší mezera na mobilech, ale stále dostatečná */
            }
        }

        @media (max-width: 480px) {
            .product-image-card .phone-img {
                transform: scale(1.5); /* Ještě menší na malých mobilech */
            }

            .product-image-card:hover .phone-img {
                transform: scale(1.55); /* Menší zvětšení na malých mobilech */
            }

            .product-image-card h3 {
                margin-top: 70px; /* Menší mezera na malých mobilech */
            }
        }

        .product-image-card .phone-glow {
            position: absolute;
            bottom: -10px;
            left: 15%;
            width: 70%;
            height: 15px;
            background: radial-gradient(ellipse at center, rgba(52, 153, 104, 0.2) 0%, rgba(52, 153, 104, 0) 70%);
            border-radius: 50%;
            z-index: 1;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .product-image-card:hover .phone-glow {
            opacity: 1;
            transform: scale(1.05);
        }

        .product-image-card h3 {
            margin-top: 120px; /* Výrazně zvětšíme mezeru pod telefony, aby se text nepřekrýval */
            font-weight: 600;
            color: var(--dark);
        }



        .integration-section {
            position: relative;
            overflow: hidden;
        }

        .integration-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center, rgba(52, 153, 104, 0.05) 0%, rgba(255, 255, 255, 0) 70%);
            z-index: 0;
        }

        .integration-container {
            position: relative;
            z-index: 1;
        }

        .integration-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
            margin-top: 50px;
        }

        .integration-card {
            background-color: var(--white);
            border-radius: 15px;
            box-shadow: var(--shadow);
            padding: 25px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            height: 150px;
        }

        .integration-card::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.4s ease;
        }

        .integration-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(52, 153, 104, 0.15);
        }

        .integration-card:hover::before {
            transform: scaleX(1);
        }

        .integration-logo {
            max-width: 80%;
            max-height: 60px;
            transition: all 0.4s ease;
        }

        .integration-card:hover .integration-logo {
            transform: scale(1.1);
        }

        .integration-title {
            margin-top: 15px;
            font-size: 14px;
            font-weight: 600;
            color: var(--gray);
            transition: all 0.3s ease;
        }

        .integration-card:hover .integration-title {
            color: var(--primary-color);
        }

        .integration-description {
            margin-top: 30px;
            text-align: center;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        @media (max-width: 992px) {
            .integration-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 768px) {
            .integration-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 576px) {
            .integration-grid {
                grid-template-columns: 1fr;
            }
        }

        .security-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
            margin-top: 50px;
        }

        .security-card {
            background-color: var(--white);
            padding: 30px;
            border-radius: 10px;
            box-shadow: var(--shadow);
            text-align: center;
            transition: all 0.5s ease;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .security-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
            transform: translateY(-100%);
            transition: transform 0.3s ease;
            z-index: 0;
        }

        .security-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(52, 153, 104, 0.15);
        }

        .security-card:hover::before {
            transform: translateY(0);
        }

        .security-card img {
            height: 80px;
            margin-bottom: 20px;
            transition: all 0.5s ease;
        }

        .security-card .security-icon {
            height: 100px;
            width: 100px;
            margin-bottom: 20px;
            transition: all 0.5s ease;
            filter: drop-shadow(0 5px 15px rgba(52, 153, 104, 0.2));
        }

        .security-card:hover .security-icon {
            transform: translateY(-10px) scale(1.05);
            filter: drop-shadow(0 10px 20px rgba(52, 153, 104, 0.3));
        }

        .faq-container {
            max-width: 800px;
            margin: 50px auto 0;
        }

        .faq-item {
            margin-bottom: 20px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .faq-question {
            background-color: var(--white);
            padding: 20px;
            cursor: pointer;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .faq-answer {
            background-color: var(--light-gray);
            padding: 0 20px;
            max-height: 0;
            overflow: hidden;
            transition: var(--transition);
        }

        .faq-answer.active {
            padding: 20px;
            max-height: 1000px;
        }

        @media (max-width: 992px) {
            .product-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .product-image-container {
                flex-direction: column;
                align-items: center;
            }

            .product-image-card {
                flex: 0 0 100%;
                margin-bottom: 30px;
            }

            .security-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .product-grid, .security-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.html" class="logo">
                    <img src="img/logo.png" alt="Growy NET logo">
                </a>
                <div class="menu-toggle" id="mobile-menu">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
                <ul class="nav-menu">
                    <li><a href="index.html" class="nav-link">Domů</a></li>
                    <li><a href="produkt.html" class="nav-link active">Produkt</a></li>
                    <li><a href="moduly.html" class="nav-link">Moduly</a></li>
                    <li><a href="pro-koho.html" class="nav-link">Pro koho</a></li>
                    <li><a href="cena.html" class="nav-link">Cena</a></li>
                    <li><a href="reference.html" class="nav-link">Reference</a></li>
                    <li><a href="blog.html" class="nav-link">Blog</a></li>
                    <li><a href="kontakt.html" class="nav-link">Kontakt</a></li>
                </ul>
                <a href="vyzkouset-zdarma.html" class="btn btn-primary">Vyzkoušet zdarma</a>
            </nav>
        </div>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1>Pomáháme Vám usnadnit komunikaci se zaměstnanci</h1>
            <p>Všechny důležité informace v jedné aplikaci, která zásadně zjednodušuje komunikaci a spolupráci zaměstnanců prostřednictvím digitalizace firemních procesů.</p>
        </div>
    </section>

    <!-- Product Packages Section -->
    <section class="product-section">
        <div class="container">
            <h2>Flexibilní stavebnicové řešení dle vašich potřeb</h2>
            <p class="section-header">Začněte se základními moduly v balíčku Start. Tento balíček si můžete rozšířit o moduly z nabídky Plus či o moduly vytvořené na míru – přesně podle Vaší potřeby.</p>

            <div class="product-grid">
                <div class="product-card">
                    <div class="product-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h3>Start</h3>
                    <p>Začnete s balíčkem základních modulů, který zjednoduší komunikaci a spolupráci zaměstnanců v každé firmě.</p>
                </div>
                <div class="product-card">
                    <div class="product-icon">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                    <h3>Plus</h3>
                    <p>Základními moduly to nekončí. Přidávejte flexibilně další moduly z nabídky podle Vašich aktuálních potřeb. Neustále pracujeme na nových modulech.</p>
                </div>
                <div class="product-card">
                    <div class="product-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <h3>Enterprise</h3>
                    <p>Nenašli jste požadovaný modul? Možná na něm právě pracujeme, nebo Vám vypracujeme individuální nabídku na míru.</p>
                </div>
            </div>

            <div class="benefits-cta">
                <a href="moduly.html" class="btn btn-primary">Prozkoumat moduly</a>
            </div>
        </div>
    </section>

    <!-- Implementation Section -->
    <section class="product-section">
        <div class="container">
            <h2>Zavedení systému Growy NET ve Vaší firmě je velmi jednoduché</h2>

            <div class="product-grid">
                <div class="product-card">
                    <div class="product-icon">
                        <i class="fas fa-clipboard-list"></i>
                    </div>
                    <h3>Příprava</h3>
                    <p>Naši specialisté vám pomohou s výběrem modulů, nastaví Growy NET a zajistí školení, aby váš tým ihned začal efektivně pracovat.</p>
                </div>
                <div class="product-card">
                    <div class="product-icon">
                        <i class="fas fa-play-circle"></i>
                    </div>
                    <h3>Spuštění</h3>
                    <p>Přechod na Growy NET bude pro Vaší společnost hračka. Pomůžeme s hromadným vytvořením přístupů i s flexibilním přidáváním modulů.</p>
                </div>
                <div class="product-card">
                    <div class="product-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3>Podpora</h3>
                    <p>Ať už potřebujete rychlou pomoc nebo detailní návod, jsme tu pro vás každý den od 8:00 do 16:00 – stačí nám jen zavolat.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- App Customization Section -->
    <section class="product-section" style="padding-top: 40px;">
        <div class="container">
            <h2>Přizpůsobte si vzhled aplikace</h2>
            <p class="section-header" style="margin-bottom: 60px;">Vzhled systému Growy NET můžete přizpůsobit potřebám Vaší značky, aby aplikace odpovídala Vaší firemní kultuře.</p>

            <div class="product-image-container" style="margin-top: 80px;">
                <div class="product-image-card">
                    <div class="phone-wrapper">
                        <img src="img/growyphone.png" alt="Growy NET aplikace" class="phone-img">
                        <div class="phone-glow"></div>
                    </div>
                    <h3>Growy NET</h3>
                </div>
                <div class="product-image-card">
                    <div class="phone-wrapper">
                        <img src="img/drbnaphone.png" alt="Drbna aplikace" class="phone-img">
                        <div class="phone-glow"></div>
                    </div>
                    <h3>Drbna</h3>
                </div>
                <div class="product-image-card">
                    <div class="phone-wrapper">
                        <img src="img/klicphone.png" alt="Jazyková škola Klíč aplikace" class="phone-img">
                        <div class="phone-glow"></div>
                    </div>
                    <h3>Jazyková škola Klíč</h3>
                </div>
            </div>
        </div>
    </section>

    <!-- Integration Section -->
    <section class="product-section integration-section">
        <div class="container integration-container">
            <h2>Propojení s jinými systémy</h2>
            <p class="section-header">Integrujte Growy NET s existujícími systémy díky našemu API konektoru. Využijte naše know-how pro propojení s ERP, HR a docházkovými systémy.</p>

            <div class="integration-grid">
                <div class="integration-card">
                    <img src="img/integration-sap.svg" alt="SAP integrace" class="integration-logo">
                    <div class="integration-title">SAP</div>
                </div>
                <div class="integration-card">
                    <img src="img/integration-abra.svg" alt="ABRA integrace" class="integration-logo">
                    <div class="integration-title">ABRA</div>
                </div>
                <div class="integration-card">
                    <img src="img/integration-helios.svg" alt="Helios integrace" class="integration-logo">
                    <div class="integration-title">Helios</div>
                </div>
                <div class="integration-card">
                    <img src="img/integration-karat.svg" alt="KARAT integrace" class="integration-logo">
                    <div class="integration-title">KARAT</div>
                </div>
                <div class="integration-card">
                    <img src="img/integration-pluxee.svg" alt="Pluxee integrace" class="integration-logo">
                    <div class="integration-title">Pluxee</div>
                </div>
                <div class="integration-card">
                    <img src="img/integration-edenred.svg" alt="Edenred integrace" class="integration-logo">
                    <div class="integration-title">Edenred</div>
                </div>
                <div class="integration-card">
                    <img src="img/integration-smeny.svg" alt="Směny.cz integrace" class="integration-logo">
                    <div class="integration-title">Směny.cz</div>
                </div>
                <div class="integration-card">
                    <img src="img/integration-anet.svg" alt="ANeT integrace" class="integration-logo">
                    <div class="integration-title">ANeT</div>
                </div>
            </div>

            <div class="integration-description">
                <p>Naše API rozhraní umožňuje bezproblémovou integraci s vašimi stávajícími systémy. Ať už používáte jakýkoliv ERP, HR nebo docházkový systém, naši specialisté vám pomohou s propojením.</p>
                <a href="kontakt.html" class="btn btn-primary" style="margin-top: 20px;">Zeptat se na možnosti integrace</a>
            </div>
        </div>
    </section>

    <!-- Security Section -->
    <section class="product-section">
        <div class="container">
            <h2>Zabezpečení a ochrana dat</h2>
            <p class="section-header">V Growy NET klademe maximální důraz na bezpečnost a ochranu vašich dat. Nabízíme komplexní zabezpečení od fyzické ochrany datových center až po šifrování a pravidelné zálohování, vše v souladu s GDPR.</p>

            <div class="security-grid">
                <div class="security-card">
                    <img src="img/security-datacenter.svg" alt="Fyzické zabezpečení" class="security-icon">
                    <h3>Fyzické zabezpečení</h3>
                    <p>Aplikaci provozujeme v nejmodernějším datovém centru v ČR s garancí vysoké bezpečnosti a dostupnosti.</p>
                </div>
                <div class="security-card">
                    <img src="img/security-app.svg" alt="Zabezpečení v aplikaci" class="security-icon">
                    <h3>Zabezpečení v aplikaci</h3>
                    <p>Šifrované datové přenosy, uživatelské jméno a heslo, nastavení síly hesla, pravidelné bezpečnostní aktualizace.</p>
                </div>
                <div class="security-card">
                    <img src="img/security-backup.svg" alt="Zálohování" class="security-icon">
                    <h3>Zálohování</h3>
                    <p>Pravidelně každý den zálohujeme data na geograficky oddělený server. Udržujeme zálohy za posledních 30 dní.</p>
                </div>
                <div class="security-card">
                    <img src="img/security-gdpr.svg" alt="GDPR" class="security-icon">
                    <h3>GDPR</h3>
                    <p>Dbáme na právní předpisy a ochranu osobních údajů. Data uchováváme v souladu se standardy GDPR.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="product-section">
        <div class="container">
            <h2>Často kladené otázky</h2>

            <div class="faq-container">
                <div class="faq-item">
                    <div class="faq-question">
                        Potřebuje zaměstnanec firemní email, aby mohl používat aplikaci?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Nepotřebuje. Pro přístup do aplikace je vyžádováno přihlášení na základě username a hesla. Jako username můžete využít firemní email, jméno a příjmení zaměstnance či třeba osobní číslo zaměstnance. Jednoduše tak do aplikace pozvete i kolegy, kteří nemají pracovní e-mail.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        Jaký je rozdíl mezi balíčky Start, Plus a Enterprise?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Balíček Start zahrnuje základní moduly pro zjednodušení komunikace a zlepšení spolupráce zaměstnanců. Jedná se o základní stavební kámen celé aplikace, který je zpoplatněn na základě počtu uživatelů, kteří aplikaci využívají. Nad rámec těchto základních modulů je možné volitelně přikupovat dodatečné moduly z balíčku Plus či objednat vývoj modulů na zakázku z balíčku Enterprise.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        Je možné omezit některé funkce pouze pro určitou skupinu uživatelů?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Ano, každý uživatel má v systému přiřazenou roli (defaultně zaměstnanec). Administrátor může vytvářet i další role a nastavovat pro ně práva na zobrazení jednotlivých modulů a dodatečná oprávnění. Je tedy možné vybrané moduly zobrazit jen určitému okruhu zaměstnanců.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        Je možné obsah cílit na vybranou skupinu uživatelů (např. dle lokality)?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Ano, v rámci jednotlivých modulů (Novinky, Událost, Směrnice, …) je možné při publikaci záznamu specifikovat odběratele například na základě jejich lokality či pracovní pozice. Jednoduše tak zobrazíte novinku, události či směrnici pouze vybrané skupině uživatelů.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        Jaká je cena za využívání aplikace?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Základní informace o zpoplatnění aplikace naleznete v části webu – Cena. Pro získání konkrétní cenové nabídky vyplňte prosím kontaktní formulář. Obratem se Vám ozve náš specialista, který Vám kromě zpracování cenové nabídky poskytne také zdarma demo účet pro vyzkoušení aplikace.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        Dokážete integrovat Growy NET s naším interním firemním systémem?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Aplikace Growy NET nabízí otevřené API pro integraci s dalšími systémy. Náš tým má bohaté zkušenosti s integracemi různých firemních systému od velkých ERP jako např. SAP po řešení ušitá na míru jednotlivým firmám. Náš integrační tým dokáže navrhnout řešení, které sami na zakázku vytvoříme případně se o realizaci může postarat externí firma s využitím existujícího API aplikace Growy NET.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
        <div class="container">
            <h2>Zaujalo Vás naše řešení?</h2>
            <p>Kontaktujte nás a vyzkoušejte aplikaci na vlastní kůži.</p>
            <a href="vyzkouset-zdarma.html" class="btn btn-primary">Vyzkoušet zdarma</a>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-top">
                <div class="footer-logo">
                    <img src="img/logo.png" alt="Growy NET logo">
                    <p>Flexibilní zaměstnanecká aplikace</p>
                    <div class="social-links">
                        <a href="https://www.linkedin.com/company/growy-net" target="_blank"><i class="fab fa-linkedin"></i></a>
                        <a href="https://www.facebook.com/growynet.cz/" target="_blank"><i class="fab fa-facebook"></i></a>
                        <a href="https://www.instagram.com/growy_net/" target="_blank"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h3>Stránky</h3>
                    <ul>
                        <li><a href="produkt.html">Produkt</a></li>
                        <li><a href="moduly.html">Moduly</a></li>
                        <li><a href="cena.html">Cena</a></li>
                        <li><a href="kontakt.html">Kontakt</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Kontakt</h3>
                    <p><strong>growy s.r.o.</strong></p>
                    <p>Na Folimance 2155/15, Praha, 120 00</p>
                    <p>+420 702 040 289, <EMAIL></p>
                    <p>IČ: 09477454, DIČ: CZ09477454</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>Copyright © 2025 Growy NET | Powered by Growy</p>
                <div class="footer-legal">
                    <a href="podminky-uzivani.html">Podmínky užívání</a>
                    <a href="zasady-zpracovani-osobnich-udaju.html">Zásady zpracování osobních údajů</a>
                    <a href="zasady-cookies.html">Zásady cookies (EU)</a>
                </div>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script src="js/animations.js"></script>
    <script>
        // FAQ Toggle
        document.addEventListener('DOMContentLoaded', function() {
            const faqQuestions = document.querySelectorAll('.faq-question');

            faqQuestions.forEach(question => {
                question.addEventListener('click', () => {
                    const answer = question.nextElementSibling;
                    const icon = question.querySelector('i');

                    answer.classList.toggle('active');
                    icon.classList.toggle('fa-chevron-up');
                    icon.classList.toggle('fa-chevron-down');
                });
            });

            // Optimalizace pro dotykové ovládání
            const touchElements = document.querySelectorAll('.btn, .nav-link, .product-card, .integration-card, .security-card, .faq-question');

            touchElements.forEach(element => {
                element.addEventListener('touchstart', function() {
                    this.classList.add('touch-active');
                }, { passive: true });

                element.addEventListener('touchend', function() {
                    this.classList.remove('touch-active');
                }, { passive: true });
            });

            // Animace telefonů při scrollování
            const phoneWrappers = document.querySelectorAll('.phone-wrapper');
            const phoneImages = document.querySelectorAll('.phone-img');

            function isInViewport(element) {
                const rect = element.getBoundingClientRect();
                return (
                    rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8 &&
                    rect.bottom >= 0
                );
            }

            function animatePhones() {
                phoneWrappers.forEach((phone, index) => {
                    if (isInViewport(phone)) {
                        setTimeout(() => {
                            phone.style.opacity = '1';
                            phone.style.transform = 'translateY(0)';
                        }, index * 200);
                    }
                });
            }

            // Nastavení počátečního stavu
            phoneWrappers.forEach(phone => {
                phone.style.opacity = '0';
                phone.style.transform = 'translateY(30px)';
                phone.style.transition = 'all 0.8s ease';
            });

            // Nastavení velikosti telefonů a základní animace
            phoneImages.forEach(img => {
                img.style.transform = 'scale(1.8)';
            });

            // Přidáme efekt stínu při najetí myší
            const phoneCards = document.querySelectorAll('.product-image-card');
            phoneCards.forEach(card => {
                card.addEventListener('mouseenter', () => {
                    const glow = card.querySelector('.phone-glow');
                    if (glow) glow.style.opacity = '1';
                });

                card.addEventListener('mouseleave', () => {
                    const glow = card.querySelector('.phone-glow');
                    if (glow) glow.style.opacity = '0';
                });
            });

            // Animace security karet
            const securityCards = document.querySelectorAll('.security-card');
            const securityIcons = document.querySelectorAll('.security-icon');

            function animateSecurityCards() {
                securityCards.forEach((card, index) => {
                    if (isInViewport(card)) {
                        setTimeout(() => {
                            card.style.opacity = '1';
                            card.style.transform = 'translateY(0)';
                        }, index * 150);
                    }
                });

                securityIcons.forEach((icon, index) => {
                    if (isInViewport(icon)) {
                        setTimeout(() => {
                            icon.style.opacity = '1';
                            icon.style.transform = 'translateY(0) scale(1)';
                        }, (index * 150) + 300);
                    }
                });
            }

            // Nastavení počátečního stavu security karet
            securityCards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.8s ease';
            });

            // Nastavení počátečního stavu security ikon
            securityIcons.forEach(icon => {
                icon.style.opacity = '0';
                icon.style.transform = 'translateY(20px) scale(0.8)';
                icon.style.transition = 'all 0.6s ease';
            });

            // Animace integračních karet
            const integrationCards = document.querySelectorAll('.integration-card');
            const integrationLogos = document.querySelectorAll('.integration-logo');

            function animateIntegrationCards() {
                integrationCards.forEach((card, index) => {
                    if (isInViewport(card)) {
                        setTimeout(() => {
                            card.style.opacity = '1';
                            card.style.transform = 'translateY(0)';
                        }, index * 100);
                    }
                });

                integrationLogos.forEach((logo, index) => {
                    if (isInViewport(logo)) {
                        setTimeout(() => {
                            logo.style.opacity = '1';
                        }, (index * 100) + 200);
                    }
                });
            }

            // Nastavení počátečního stavu integračních karet
            integrationCards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s ease';
            });

            // Nastavení počátečního stavu integračních log
            integrationLogos.forEach(logo => {
                logo.style.opacity = '0';
                logo.style.transition = 'all 0.6s ease';
            });

            // Spuštění animace při scrollování
            window.addEventListener('scroll', animatePhones);
            window.addEventListener('scroll', animateSecurityCards);
            window.addEventListener('scroll', animateIntegrationCards);
            // Spuštění animace při načtení stránky
            animatePhones();
            animateSecurityCards();
            animateIntegrationCards();
        });
    </script>
</body>
</html>
