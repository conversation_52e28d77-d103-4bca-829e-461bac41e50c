<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Kontaktujte tým Growy NET - Jsme tu pro vás každý pracovní den od 8:00 do 16:00. Domluvte si online schůzku nebo nás kontaktujte telefonicky či e-mailem.">
    <title>Kontakt - Growy NET | Flexibilní zaměstnanecká aplikace</title>
    <link rel="icon" type="image/svg+xml" href="img/favicon.svg">
    <link rel="stylesheet" href="css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Page Specific Styles */
        .page-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            padding-top: 120px; /* 80px (výška hlavičky) + 40px (dodatečný prostor) */
            padding-bottom: 80px;
            text-align: center;
            color: var(--white);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23ffffff" fill-opacity="0.05" d="M0,192L48,176C96,160,192,128,288,122.7C384,117,480,139,576,165.3C672,192,768,224,864,213.3C960,203,1056,149,1152,138.7C1248,128,1344,160,1392,176L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
            background-position: bottom;
            background-repeat: no-repeat;
            background-size: cover;
            opacity: 0.3;
        }

        .page-header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            position: relative;
        }

        .page-header p {
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
            position: relative;
        }

        /* Contact Cards Section */
        /* Team Section */
        .team-section {
            padding: 100px 0 80px;
            position: relative;
            background-color: #f9f9f9;
        }

        .team-section::after {
            content: '';
            position: absolute;
            bottom: -50px;
            left: 0;
            width: 100%;
            height: 100px;
            background-color: #f9f9f9;
            clip-path: polygon(0 0, 100% 0, 100% 50%, 0 100%);
            z-index: 1;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 50px;
            position: relative;
            z-index: 2;
        }

        .team-card {
            background-color: var(--white);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .team-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(52, 153, 104, 0.15);
        }

        .team-card-image {
            width: 100%;
            height: 0;
            padding-bottom: 100%;
            position: relative;
            overflow: hidden;
        }

        .team-card-image img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.5s ease;
            background-color: #f5f5f5;
        }

        .team-card-image img[src$=".svg"] {
            object-fit: contain;
            padding: 10%;
        }

        .team-card:hover .team-card-image img {
            transform: scale(1.05);
        }

        .team-card-content {
            padding: 25px;
            text-align: center;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .team-card h3 {
            margin-bottom: 5px;
            font-size: 1.3rem;
            color: var(--dark);
            font-weight: 600;
        }

        .team-position {
            color: var(--primary-color);
            font-weight: 500;
            margin-bottom: 15px;
            font-size: 1rem;
        }

        .team-contact {
            margin-top: 15px;
        }

        .team-contact a {
            display: block;
            color: var(--gray);
            margin-bottom: 8px;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 0.9rem;
        }

        .team-contact a:hover {
            color: var(--primary-color);
            transform: translateX(3px);
        }

        .team-contact a i {
            margin-right: 8px;
            color: var(--primary-color);
        }

        @media (max-width: 992px) {
            .team-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 576px) {
            .team-grid {
                grid-template-columns: 1fr;
            }

            .team-card {
                max-width: 320px;
                margin-left: auto;
                margin-right: auto;
            }
        }

        /* Contact Section */
        .contact-section {
            padding: 100px 0 80px;
            position: relative;
            margin-top: 50px;
        }

        .contact-section::before {
            content: '';
            position: absolute;
            top: -80px;
            left: 0;
            width: 100%;
            height: 80px;
            background-color: var(--white);
            border-radius: 50% 50% 0 0;
            z-index: 1;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 50px;
        }

        .contact-card {
            background-color: var(--white);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            text-align: center;
            transition: all 0.3s ease;
            overflow: hidden;
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .contact-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(52, 153, 104, 0.15);
        }

        .contact-card-header {
            background-color: var(--primary-color);
            padding: 30px 20px;
            color: var(--white);
            position: relative;
        }

        .contact-card:nth-child(1) .contact-card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #40b37e 100%);
        }

        .contact-card:nth-child(2) .contact-card-header {
            background: linear-gradient(135deg, #349968 0%, var(--primary-dark) 100%);
        }

        .contact-card:nth-child(3) .contact-card-header {
            background: linear-gradient(135deg, #40b37e 0%, #349968 100%);
        }

        .contact-card-icon {
            width: 70px;
            height: 70px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 1.8rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .contact-card-content {
            padding: 30px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .contact-card h3 {
            margin-bottom: 15px;
            font-size: 1.4rem;
            color: var(--dark);
        }

        .contact-card-info {
            margin-bottom: 20px;
        }

        .contact-card a {
            display: inline-flex;
            align-items: center;
            color: var(--primary-color);
            margin-bottom: 10px;
            transition: all 0.3s ease;
            font-weight: 500;
            text-decoration: none;
        }

        .contact-card a i {
            margin-right: 8px;
            font-size: 1.1rem;
        }

        .contact-card a:hover {
            color: var(--primary-dark);
            transform: translateX(3px);
        }

        .contact-card p {
            margin-bottom: 10px;
            color: var(--gray);
        }

        .contact-card-action {
            margin-top: 20px;
        }

        .contact-card-action .btn {
            width: 100%;
            padding: 12px 20px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Map Section */
        .map-section {
            padding: 0 0 80px;
        }

        .map-container {
            height: 400px;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            position: relative;
        }

        .map-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        .map-overlay {
            position: absolute;
            top: 20px;
            left: 20px;
            background-color: var(--white);
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            max-width: 300px;
        }

        .map-overlay h3 {
            margin-bottom: 10px;
            font-size: 1.2rem;
            color: var(--dark);
        }

        .map-overlay p {
            margin-bottom: 5px;
            font-size: 0.9rem;
            color: var(--gray);
        }

        /* Online Meeting Section */
        .meeting-section {
            padding: 80px 0 0 0; /* Odstraníme padding dole */
            background-color: #f9f9f9;
            position: relative;
            overflow: hidden;
            margin-bottom: 0; /* Odstraníme margin dole */
            border-bottom: none; /* Odstraníme čáru dole */
            min-height: 300px; /* Ještě více snížíme minimální výšku sekce */
        }

        .meeting-section::before {
            content: '';
            position: absolute;
            top: -150px;
            right: -150px;
            width: 500px;
            height: 500px;
            background-color: rgba(52, 153, 104, 0.05);
            border-radius: 50%;
            z-index: 0;
        }

        .meeting-section::after {
            content: '';
            position: absolute;
            bottom: -100px;
            left: -100px;
            width: 300px;
            height: 300px;
            background-color: rgba(52, 153, 104, 0.03);
            border-radius: 50%;
            z-index: 0;
        }

        .meeting-container {
            display: flex;
            align-items: center;
            gap: 50px;
            position: relative;
            z-index: 1;
            max-width: 1100px;
            margin: 0 auto;
        }

        .meeting-image {
            flex: 0 0 40%;
        }

        .meeting-image img {
            width: 100%;
            transition: all 0.3s ease;
            filter: drop-shadow(0 10px 25px rgba(52, 153, 104, 0.2));
        }

        .meeting-image img[src$=".svg"] {
            background-color: transparent;
            padding: 0;
            max-height: 450px;
            object-fit: contain;
            position: relative;
            margin-bottom: 0; /* Odstraníme margin dole */
            margin-top: -20px; /* Posune obrázek trochu nahoru */
        }

        .meeting-image:hover img {
            filter: drop-shadow(0 15px 30px rgba(52, 153, 104, 0.4));
            transform: scale(1.12) !important;
        }

        .meeting-content {
            flex: 0 0 60%;
        }

        .meeting-content h2 {
            margin-bottom: 20px;
            font-size: 2rem;
            color: var(--dark);
        }

        .meeting-content p {
            margin-bottom: 30px;
            font-size: 1.1rem;
            color: var(--gray);
            line-height: 1.6;
        }

        .meeting-cta {
            display: inline-flex;
            align-items: center;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--white);
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(52, 153, 104, 0.2);
        }

        .meeting-cta i {
            margin-right: 10px;
            font-size: 1.2rem;
        }

        .meeting-cta:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(52, 153, 104, 0.3);
        }

        .meeting-features {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 30px;
        }

        .meeting-feature {
            flex: 0 0 calc(50% - 10px);
            display: flex;
            align-items: flex-start;
        }

        .meeting-feature i {
            color: var(--primary-color);
            font-size: 1.2rem;
            margin-right: 10px;
            margin-top: 3px;
        }

        .meeting-feature p {
            margin-bottom: 0;
            font-size: 1rem;
        }

        /* Form Section */
        .form-section {
            padding: 50px 0 30px 0; /* Přidáme padding nahoře */
            background-color: var(--white);
            position: relative;
        }

        .section-transition {
            display: none; /* Skryjeme přechod */
        }

        .form-section::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 0; /* Odstraníme výšku přechodu */
            background-color: #f9f9f9;
            z-index: 0;
        }

        .contact-form-container {
            position: relative;
            z-index: 1;
        }

        .contact-form {
            max-width: 800px;
            margin: 50px auto 0;
            background-color: var(--white);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
        }

        .contact-form::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }

        .form-grid .form-group:last-child {
            grid-column: span 2;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--dark);
        }

        .form-control {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid rgba(0,0,0,0.08);
            border-radius: 10px;
            font-family: 'Poppins', sans-serif;
            font-size: 1rem;
            transition: all 0.3s ease;
            background-color: #f9f9f9;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(52, 153, 104, 0.1);
            background-color: var(--white);
        }

        textarea.form-control {
            min-height: 150px;
            resize: vertical;
        }

        .form-check {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .form-check input {
            margin-right: 10px;
            margin-top: 5px;
        }

        .form-check label {
            font-size: 0.9rem;
            color: var(--gray);
            line-height: 1.5;
        }

        .form-check a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .form-check a:hover {
            color: var(--primary-dark);
        }

        .form-button {
            text-align: center;
        }

        .form-button .btn {
            padding: 15px 40px;
            font-weight: 600;
            font-size: 1rem;
            border-radius: 50px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 5px 15px rgba(52, 153, 104, 0.2);
            transition: all 0.3s ease;
        }

        .form-button .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(52, 153, 104, 0.3);
        }

        /* Story Section */
        .story-section {
            padding: 0 0 100px 0; /* Odstraníme padding nahoře */
            background-color: #f9f9f9;
            position: relative;
        }

        .story-transition {
            height: 30px; /* Výška přechodu */
        }

        .story-container {
            display: flex;
            align-items: center;
            gap: 50px;
            position: relative;
            z-index: 1;
        }

        .story-image {
            flex: 0 0 40%;
            position: relative;
        }

        .story-image::before {
            content: '';
            position: absolute;
            top: -20px;
            left: -20px;
            width: 100%;
            height: 100%;
            border: 2px solid var(--primary-color);
            border-radius: 30px;
            z-index: -1;
            transition: all 0.3s ease;
        }

        .story-image:hover::before {
            top: -15px;
            left: -15px;
            border-color: var(--primary-dark);
        }

        .story-image img {
            width: 100%;
            border-radius: 30px;
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .story-image:hover img {
            transform: scale(1.02);
            box-shadow: 0 20px 40px rgba(52, 153, 104, 0.15);
        }

        .story-content {
            flex: 0 0 60%;
        }

        .story-content h2 {
            margin-bottom: 20px;
            font-size: 2rem;
            color: var(--dark);
            position: relative;
            display: inline-block;
        }

        .story-content h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 50px;
            height: 3px;
            background-color: var(--primary-color);
        }

        .story-content h3 {
            margin-bottom: 20px;
            font-size: 1.5rem;
            color: var(--primary-dark);
        }

        .story-content p {
            margin-bottom: 20px;
            font-size: 1.1rem;
            color: var(--gray);
            line-height: 1.6;
        }

        .story-team {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 25px;
            margin-top: 40px;
        }

        .team-member {
            text-align: center;
            flex: 0 0 calc(33.333% - 25px);
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .team-member:hover {
            transform: translateY(-5px);
        }

        .team-member img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 12px;
            border: 3px solid var(--white);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .team-member:hover img {
            border-color: var(--primary-color);
            box-shadow: 0 8px 20px rgba(52, 153, 104, 0.2);
        }

        .team-member h4 {
            margin-bottom: 5px;
            font-size: 1.1rem;
            color: var(--dark);
            font-weight: 600;
        }

        .team-member p {
            font-size: 0.9rem;
            color: var(--primary-color);
            margin-bottom: 0;
            font-weight: 500;
        }

        @media (max-width: 992px) {
            .team-member {
                flex: 0 0 calc(50% - 25px);
            }
        }

        @media (max-width: 576px) {
            .team-member {
                flex: 0 0 calc(100% - 25px);
                max-width: 200px;
                margin-left: auto;
                margin-right: auto;
            }
        }

        /* FAQ Section */
        .faq-section {
            padding: 100px 0;
            background-color: var(--white);
        }

        .faq-container {
            max-width: 800px;
            margin: 50px auto 0;
        }

        .faq-item {
            margin-bottom: 20px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            background-color: var(--white);
            border: 1px solid rgba(0,0,0,0.05);
        }

        .faq-question {
            padding: 20px;
            background-color: var(--white);
            color: var(--dark);
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
        }

        .faq-question:hover {
            background-color: #f9f9f9;
        }

        .faq-question i {
            color: var(--primary-color);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .faq-answer {
            padding: 0 20px;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
            background-color: #f9f9f9;
        }

        .faq-answer.active {
            padding: 20px;
            max-height: 1000px;
        }

        .faq-answer p {
            margin-bottom: 0;
            color: var(--gray);
            line-height: 1.6;
        }

        /* Responsive Styles */
        @media (max-width: 1200px) {
            .meeting-feature {
                flex: 0 0 100%;
            }
        }

        @media (max-width: 992px) {
            .page-header h1 {
                font-size: 2.5rem;
            }

            .contact-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .meeting-container, .story-container {
                flex-direction: column;
            }

            .meeting-image, .meeting-content, .story-image, .story-content {
                flex: 0 0 100%;
            }

            .story-image::before {
                display: none;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .form-grid .form-group:last-child {
                grid-column: span 1;
            }
        }

        @media (max-width: 768px) {
            .page-header {
                padding-top: 110px; /* 70px (výška hlavičky na mobilu) + 40px (dodatečný prostor) */
                padding-bottom: 60px;
            }

            .page-header h1 {
                font-size: 2rem;
            }

            .contact-grid {
                grid-template-columns: 1fr;
            }

            .contact-card {
                max-width: 400px;
                margin: 0 auto;
            }

            .contact-form {
                padding: 30px 20px;
            }

            .map-overlay {
                position: relative;
                top: 0;
                left: 0;
                max-width: 100%;
                margin-bottom: 20px;
            }

            .story-team {
                flex-wrap: wrap;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .page-header h1 {
                font-size: 1.8rem;
            }

            .page-header p {
                font-size: 1rem;
            }

            .contact-card-header {
                padding: 20px 15px;
            }

            .contact-card-icon {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }

            .contact-card-content {
                padding: 20px 15px;
            }

            .contact-card h3 {
                font-size: 1.2rem;
            }

            .meeting-cta {
                padding: 12px 20px;
                font-size: 0.9rem;
            }

            .form-button .btn {
                padding: 12px 30px;
                font-size: 0.9rem;
            }

            .story-content h2 {
                font-size: 1.8rem;
            }

            .story-content h3 {
                font-size: 1.3rem;
            }

            .story-content p {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.html" class="logo">
                    <img src="img/logo.png" alt="Growy NET logo">
                </a>
                <div class="menu-toggle" id="mobile-menu">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
                <ul class="nav-menu">
                    <li><a href="index.html" class="nav-link">Domů</a></li>
                    <li><a href="produkt.html" class="nav-link">Produkt</a></li>
                    <li><a href="moduly.html" class="nav-link">Moduly</a></li>
                    <li><a href="pro-koho.html" class="nav-link">Pro koho</a></li>
                    <li><a href="cena.html" class="nav-link">Cena</a></li>
                    <li><a href="reference.html" class="nav-link">Reference</a></li>
                    <li><a href="blog.html" class="nav-link">Blog</a></li>
                    <li><a href="kontakt.html" class="nav-link active">Kontakt</a></li>
                </ul>
                <a href="vyzkouset-zdarma.html" class="btn btn-primary">Vyzkoušet zdarma</a>
            </nav>
        </div>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1>Kontaktujte nás</h1>
            <p>Máte jakékoliv otázky? Kontaktujte nás a my Vám rádi poradíme.</p>
        </div>
    </section>

    <!-- Team Members Section -->
    <section class="team-section">
        <div class="container">
            <h2>Náš tým</h2>
            <p class="section-header">Seznamte se s lidmi, kteří stojí za Growy NET. Jsme tu pro vás a rádi vám pomůžeme s jakýmkoliv dotazem.</p>

            <div class="team-grid">
                <div class="team-card">
                    <div class="team-card-image">
                        <img src="img/team-member-tomas.svg" alt="Tomáš Vrchota">
                    </div>
                    <div class="team-card-content">
                        <h3>Tomáš Vrchota</h3>
                        <p class="team-position">Obchodní ředitel</p>
                        <div class="team-contact">
                            <a href="tel:+420702040289"><i class="fas fa-phone-alt"></i> +420 702 040 289</a>
                            <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a>
                        </div>
                    </div>
                </div>

                <div class="team-card">
                    <div class="team-card-image">
                        <img src="img/Kateřina Čulíková.svg" alt="Kateřina Čulíková">
                    </div>
                    <div class="team-card-content">
                        <h3>Kateřina Čulíková</h3>
                        <p class="team-position">Zákaznická podpora</p>
                        <div class="team-contact">
                            <a href="tel:+420725864778"><i class="fas fa-phone-alt"></i> +420 725 864 778</a>
                            <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a>
                        </div>
                    </div>
                </div>

                <div class="team-card">
                    <div class="team-card-image">
                        <img src="img/team-member-marketa.svg" alt="Markéta Urbanová">
                    </div>
                    <div class="team-card-content">
                        <h3>Markéta Urbanová</h3>
                        <p class="team-position">Produktový manažer</p>
                        <div class="team-contact">
                            <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a>
                        </div>
                    </div>
                </div>

                <div class="team-card">
                    <div class="team-card-image">
                        <img src="img/team-member-josef.svg" alt="Josef Zeman">
                    </div>
                    <div class="team-card-content">
                        <h3>Josef Zeman</h3>
                        <p class="team-position">Marketingový specialista</p>
                        <div class="team-contact">
                            <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a>
                        </div>
                    </div>
                </div>

                <div class="team-card">
                    <div class="team-card-image">
                        <img src="img/team-member-jakub.svg" alt="Jakub Zemčík">
                    </div>
                    <div class="team-card-content">
                        <h3>Jakub Zemčík</h3>
                        <p class="team-position">Key Account Manager</p>
                        <div class="team-contact">
                            <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a>
                        </div>
                    </div>
                </div>

                <div class="team-card">
                    <div class="team-card-image">
                        <img src="img/team-member-lubos.svg" alt="Luboš Petráň">
                    </div>
                    <div class="team-card-content">
                        <h3>Luboš Petráň</h3>
                        <p class="team-position">Key Account Manager</p>
                        <div class="team-contact">
                            <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Info Section -->
    <section class="contact-section">
        <div class="container">
            <h2>Jak nás můžete kontaktovat</h2>
            <p class="section-header">Vyberte si způsob komunikace, který vám nejvíce vyhovuje. Jsme tu pro vás každý pracovní den.</p>

            <div class="contact-grid">
                <div class="contact-card">
                    <div class="contact-card-header">
                        <div class="contact-card-icon">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <h3>Obchod</h3>
                    </div>
                    <div class="contact-card-content">
                        <div class="contact-card-info">
                            <p>Máte zájem o naše služby nebo potřebujete cenovou nabídku?</p>
                            <a href="tel:+420702040289"><i class="fas fa-phone-alt"></i> +420 702 040 289</a>
                            <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a>
                        </div>
                        <div class="contact-card-action">
                            <a href="#meeting" class="btn btn-outline">Sjednat schůzku</a>
                        </div>
                    </div>
                </div>

                <div class="contact-card">
                    <div class="contact-card-header">
                        <div class="contact-card-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h3>Zákaznická podpora</h3>
                    </div>
                    <div class="contact-card-content">
                        <div class="contact-card-info">
                            <p>Potřebujete pomoc s používáním aplikace?</p>
                            <p><i class="far fa-clock"></i> Pracovní dny 8:00 - 16:00</p>
                            <a href="tel:+420725864778"><i class="fas fa-phone-alt"></i> +420 725 864 778</a>
                            <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a>
                        </div>
                        <div class="contact-card-action">
                            <a href="#form" class="btn btn-outline">Kontaktní formulář</a>
                        </div>
                    </div>
                </div>

                <div class="contact-card">
                    <div class="contact-card-header">
                        <div class="contact-card-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <h3>Fakturační údaje</h3>
                    </div>
                    <div class="contact-card-content">
                        <div class="contact-card-info">
                            <p><i class="fas fa-map-marker-alt"></i> Na Folimance 2155/15, Praha, 120 00</p>
                            <p><i class="fas fa-id-card"></i> IČ: ********</p>
                            <p><i class="fas fa-file-invoice"></i> DIČ: CZ********</p>
                            <p><i class="fas fa-university"></i> Bankovní spojení: na vyžádání</p>
                        </div>
                        <div class="contact-card-action">
                            <a href="#map" class="btn btn-outline">Zobrazit na mapě</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section -->
    <section id="map" class="map-section">
        <div class="container">
            <div class="map-container">
                <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2560.*************!2d14.4236903!3d50.0699863!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x470b94f4f7d3a2a7%3A0x5c6a0e6d3e4b8f5d!2sNa%20Folimance%202155%2F15%2C%20120%2000%20Praha%202-Nov%C3%A9%20M%C4%9Bsto!5e0!3m2!1scs!2scz!4v1651234567890!5m2!1scs!2scz" allowfullscreen="" loading="lazy"></iframe>
                <div class="map-overlay">
                    <h3>Kde nás najdete</h3>
                    <p><strong>growy s.r.o.</strong></p>
                    <p>Na Folimance 2155/15</p>
                    <p>Praha 2 - Nové Město, 120 00</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Online Meeting Section -->
    <section id="meeting" class="meeting-section">
        <div class="container">
            <div class="meeting-container">
                <div class="meeting-image">
                    <img src="img/schuzka.svg" alt="Online schůzka" style="max-width: 100%; height: auto; display: block; margin: 0 auto; transform: scale(1.1);">
                </div>
                <div class="meeting-content">
                    <h2>Sjednejte si online schůzku</h2>
                    <p>Nemáte čas na osobní setkání? Nevadí! Můžeme se spojit online a představit vám naše řešení, zodpovědět vaše dotazy nebo vám pomoci s nastavením aplikace.</p>

                    <a href="https://calendly.com/growynet/30min" target="_blank" class="meeting-cta">
                        <i class="far fa-calendar-alt"></i> Rezervovat termín schůzky
                    </a>

                    <div class="meeting-features">
                        <div class="meeting-feature">
                            <i class="fas fa-check-circle"></i>
                            <p>Flexibilní termíny dle vašich možností</p>
                        </div>
                        <div class="meeting-feature">
                            <i class="fas fa-check-circle"></i>
                            <p>Žádné cestování, schůzka z pohodlí vaší kanceláře</p>
                        </div>
                        <div class="meeting-feature">
                            <i class="fas fa-check-circle"></i>
                            <p>Možnost sdílení obrazovky pro lepší prezentaci</p>
                        </div>
                        <div class="meeting-feature">
                            <i class="fas fa-check-circle"></i>
                            <p>Záznam schůzky na vyžádání</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Form Section -->
    <section id="form" class="form-section">
        <div class="section-transition"></div>
        <div class="container">
            <h2 style="margin-top: 0;">Napište nám</h2>
            <p class="section-header">Vyplňte kontaktní formulář a my se vám ozveme co nejdříve. Rádi vám poskytneme více informací o našem řešení nebo vám připravíme demo účet pro vyzkoušení.</p>

            <div class="contact-form-container">
                <form class="contact-form" action="#" method="POST">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="name">Jméno a příjmení *</label>
                            <input type="text" id="name" name="name" class="form-control" placeholder="Zadejte vaše jméno" required>
                        </div>
                        <div class="form-group">
                            <label for="company">Název společnosti *</label>
                            <input type="text" id="company" name="company" class="form-control" placeholder="Zadejte název vaší společnosti" required>
                        </div>
                        <div class="form-group">
                            <label for="email">E-mail *</label>
                            <input type="email" id="email" name="email" class="form-control" placeholder="<EMAIL>" required>
                        </div>
                        <div class="form-group">
                            <label for="phone">Telefon</label>
                            <input type="tel" id="phone" name="phone" class="form-control" placeholder="+420 XXX XXX XXX">
                        </div>
                        <div class="form-group">
                            <label for="employees">Počet zaměstnanců</label>
                            <select id="employees" name="employees" class="form-control">
                                <option value="">Vyberte počet zaměstnanců</option>
                                <option value="1-50">1-50</option>
                                <option value="51-100">51-100</option>
                                <option value="101-200">101-200</option>
                                <option value="201-500">201-500</option>
                                <option value="501+">501+</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="subject">Předmět *</label>
                            <select id="subject" name="subject" class="form-control" required>
                                <option value="">Vyberte předmět zprávy</option>
                                <option value="demo">Zájem o demo</option>
                                <option value="pricing">Cenová nabídka</option>
                                <option value="support">Technická podpora</option>
                                <option value="feedback">Zpětná vazba</option>
                                <option value="other">Jiné</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="message">Zpráva *</label>
                            <textarea id="message" name="message" class="form-control" placeholder="Napište nám, s čím vám můžeme pomoci..." required></textarea>
                        </div>
                    </div>

                    <div class="form-check">
                        <input type="checkbox" id="gdpr" name="gdpr" required>
                        <label for="gdpr">Souhlasím se <a href="zasady-zpracovani-osobnich-udaju.html" target="_blank">zpracováním osobních údajů</a>. Vaše údaje budou použity pouze pro účely odpovědi na váš dotaz a nebudou sdíleny s třetími stranami. *</label>
                    </div>

                    <div class="form-check">
                        <input type="checkbox" id="newsletter" name="newsletter">
                        <label for="newsletter">Mám zájem o zasílání novinek a aktualizací o produktu Growy NET (max. 1x měsíčně).</label>
                    </div>

                    <div class="form-button">
                        <button type="submit" class="btn btn-primary">Odeslat zprávu</button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Our Story Section -->
    <section class="story-section">
        <div class="story-transition"></div>
        <div class="container">
            <h2 style="margin-top: 0;">Náš příběh</h2>

            <div class="story-container">
                <div class="story-image">
                    <img src="img/our-story.JPG" alt="Náš příběh">
                </div>
                <div class="story-content">
                    <h3>Lidé jsou klíčem k úspěchu v podnikání</h3>
                    <p>Growy NET vznikl jako náš interní systém, díky kterému jsme měli vždy aktuální informace, ať už se jednalo o výběr termínu pro další firemní teambuilding, nebo o anketu ohledně nového designu triček. Každý znal své dlouholeté kolegy i nově příchozí zaměstnance, což skvěle oživilo naši firemní kulturu.</p>
                    <p>Časem jsme zjistili, že problém s obecným předáváním informací nemáme jen my, ale i spousta dalších firem. Tak jsme našemu nejbližšímu zákazníkovi nabídli produkt na zkoušku. A hádejte. Byli nadšení!</p>
                    <p>Growy NET se jim hodně líbil a výrazně tak zlepšil jejich interní komunikaci. Proto jsme se rozhodli z našeho firemního intranetu udělat produkt a začít ho nabízet i ostatním firmám, kterým by mohl také pomoci.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section">
        <div class="container">
            <h2>Často kladené dotazy</h2>
            <p class="section-header">Zde najdete odpovědi na nejčastější otázky. Pokud zde nenajdete odpověď na vaši otázku, neváhejte nás kontaktovat.</p>

            <div class="faq-container">
                <div class="faq-item">
                    <div class="faq-question">
                        Jak dlouho trvá implementace Growy NET?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Základní implementace Growy NET trvá přibližně 2-3 týdny. Zahrnuje nastavení aplikace, import dat a základní školení. V případě složitějších implementací s vlastními moduly může být doba delší, vždy záleží na konkrétních požadavcích.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        Mohu si Growy NET vyzkoušet před nákupem?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Ano, nabízíme možnost vyzkoušet si Growy NET zdarma po dobu 30 dnů. Během této doby máte přístup ke všem funkcím a můžete si ověřit, zda aplikace vyhovuje vašim potřebám. Stačí vyplnit kontaktní formulář a my vám připravíme demo účet.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        Jak je to s bezpečností dat v Growy NET?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Bezpečnost dat je pro nás prioritou. Všechna data jsou šifrována a ukládána na zabezpečených serverech v EU. Pravidelně provádíme bezpečnostní audity a zálohy dat. Splňujeme všechny požadavky GDPR a máme implementované procesy pro ochranu osobních údajů.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        Jaké jsou možnosti integrace s jinými systémy?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Growy NET nabízí možnost integrace s řadou běžně používaných systémů jako jsou HR systémy, CRM, ERP a další. Máme otevřené API, které umožňuje propojení s vašimi stávajícími systémy. Pro specifické požadavky na integraci nás kontaktujte a rádi vám připravíme individuální řešení.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        Jak probíhá školení uživatelů?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>V rámci implementace poskytujeme základní školení pro administrátory a klíčové uživatele. Dále nabízíme online školení pro všechny zaměstnance a komplexní dokumentaci včetně video návodů. Pro větší firmy můžeme zajistit i osobní školení přímo u vás ve firmě.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-top">
                <div class="footer-logo">
                    <img src="img/logo.png" alt="Growy NET logo">
                    <p>Flexibilní zaměstnanecká aplikace</p>
                    <div class="social-links">
                        <a href="https://www.linkedin.com/company/growy-net" target="_blank"><i class="fab fa-linkedin"></i></a>
                        <a href="https://www.facebook.com/growynet.cz/" target="_blank"><i class="fab fa-facebook"></i></a>
                        <a href="https://www.instagram.com/growy_net/" target="_blank"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h3>Stránky</h3>
                    <ul>
                        <li><a href="produkt.html">Produkt</a></li>
                        <li><a href="moduly.html">Moduly</a></li>
                        <li><a href="cena.html">Cena</a></li>
                        <li><a href="kontakt.html">Kontakt</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Kontakt</h3>
                    <p><strong>growy s.r.o.</strong></p>
                    <p>Na Folimance 2155/15, Praha, 120 00</p>
                    <p>+420 702 040 289, <EMAIL></p>
                    <p>IČ: ********, DIČ: CZ********</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>Copyright © 2025 Growy NET | Powered by Growy</p>
                <div class="footer-legal">
                    <a href="podminky-uzivani.html">Podmínky užívání</a>
                    <a href="zasady-zpracovani-osobnich-udaju.html">Zásady zpracování osobních údajů</a>
                    <a href="zasady-cookies.html">Zásady cookies (EU)</a>
                </div>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script src="js/animations.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // FAQ Toggle
            const faqQuestions = document.querySelectorAll('.faq-question');

            faqQuestions.forEach(question => {
                question.addEventListener('click', () => {
                    const answer = question.nextElementSibling;
                    const icon = question.querySelector('i');

                    // Close all other answers
                    document.querySelectorAll('.faq-answer').forEach(item => {
                        if (item !== answer) {
                            item.classList.remove('active');
                        }
                    });

                    // Reset all icons
                    document.querySelectorAll('.faq-question i').forEach(i => {
                        if (i !== icon) {
                            i.classList.remove('fa-chevron-up');
                            i.classList.add('fa-chevron-down');
                        }
                    });

                    // Toggle current answer
                    answer.classList.toggle('active');
                    icon.classList.toggle('fa-chevron-up');
                    icon.classList.toggle('fa-chevron-down');
                });
            });
        });
    </script>
</body>
</html>
