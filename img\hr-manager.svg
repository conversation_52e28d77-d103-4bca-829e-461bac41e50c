<?xml version="1.0" encoding="UTF-8"?>
<svg width="300px" height="300px" viewBox="0 0 300 300" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>HR mana<PERSON>er</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="hrGradient">
            <stop stop-color="#349968" offset="0%"></stop>
            <stop stop-color="#0D5533" offset="100%"></stop>
        </linearGradient>
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur in="SourceAlpha" stdDeviation="3"></feGaussianBlur>
            <feOffset dx="0" dy="3" result="offsetblur"></feOffset>
            <feComponentTransfer>
                <feFuncA type="linear" slope="0.3"></feFuncA>
            </feComponentTransfer>
            <feMerge>
                <feMergeNode></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Background Circle -->
        <circle fill="#F5F5F5" cx="150" cy="150" r="150"></circle>
        
        <!-- HR Text -->
        <text x="60" y="100" font-family="Arial" font-size="36" font-weight="bold" fill="url(#hrGradient)">HR</text>
        <text x="60" y="140" font-family="Arial" font-size="24" fill="#666666">manažer</text>
        
        <!-- Person Icon -->
        <circle fill="#FFFFFF" cx="180" cy="120" r="40" filter="url(#shadow)"></circle>
        <circle fill="url(#hrGradient)" cx="180" cy="105" r="15"></circle>
        <path d="M155,160 C155,140 165,130 180,130 C195,130 205,140 205,160" fill="url(#hrGradient)"></path>
        
        <!-- Document/Clipboard -->
        <rect fill="#FFFFFF" x="100" y="170" width="100" height="120" rx="5" filter="url(#shadow)"></rect>
        <rect fill="url(#hrGradient)" x="100" y="170" width="100" height="20" rx="5"></rect>
        
        <!-- Document Lines -->
        <line x1="120" y1="210" x2="180" y2="210" stroke="#CCCCCC" stroke-width="2" stroke-linecap="round"></line>
        <line x1="120" y1="230" x2="180" y2="230" stroke="#CCCCCC" stroke-width="2" stroke-linecap="round"></line>
        <line x1="120" y1="250" x2="160" y2="250" stroke="#CCCCCC" stroke-width="2" stroke-linecap="round"></line>
        
        <!-- Checkmark -->
        <circle fill="#FFFFFF" cx="220" cy="220" r="25" filter="url(#shadow)"></circle>
        <path d="M210,220 L218,228 L230,212" stroke="url(#hrGradient)" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path>
    </g>
</svg>
