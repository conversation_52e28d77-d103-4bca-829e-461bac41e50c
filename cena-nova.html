<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Growy NET - Flexibilní zaměstnanecká aplikace pro interní komunikaci a digitalizaci firemních procesů">
    <title>Cena - Growy NET | Flexibilní zaměstnanecká aplikace</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Page Specific Styles */
        .page-header {
            background-color: var(--light-gray);
            padding-top: 150px;
            padding-bottom: 50px;
            text-align: center;
        }

        .pricing-section {
            padding: 80px 0;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 50px;
        }

        .pricing-card {
            background-color: var(--white);
            border-radius: 10px;
            box-shadow: var(--shadow);
            overflow: hidden;
            transition: var(--transition);
            height: 100%;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .pricing-card:hover {
            transform: translateY(-5px);
        }

        .pricing-card.popular::before {
            content: "Nejoblíbenější";
            position: absolute;
            top: 15px;
            right: -30px;
            background-color: var(--primary-dark);
            color: var(--white);
            padding: 5px 30px;
            font-size: 0.8rem;
            font-weight: 600;
            transform: rotate(45deg);
            z-index: 1;
        }

        .pricing-header {
            background-color: var(--primary-color);
            color: var(--white);
            padding: 30px;
            text-align: center;
        }

        .pricing-header h3 {
            margin-bottom: 10px;
            font-size: 1.8rem;
        }

        .pricing-price {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .pricing-price span {
            font-size: 1rem;
            font-weight: 400;
        }

        .pricing-content {
            padding: 30px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .pricing-features {
            margin-bottom: 30px;
            flex: 1;
        }

        .pricing-features li {
            margin-bottom: 15px;
            position: relative;
            padding-left: 30px;
        }

        .pricing-features li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: var(--primary-color);
            font-weight: 700;
        }

        .pricing-button {
            text-align: center;
            margin-top: auto;
        }

        .pricing-toggle {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 40px;
        }

        .toggle-label {
            font-weight: 600;
            margin: 0 15px;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: var(--primary-color);
        }

        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }

        .annual-discount {
            display: inline-block;
            background-color: var(--primary-dark);
            color: var(--white);
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            margin-left: 10px;
        }

        .benefits-section {
            padding: 80px 0;
            background-color: var(--light-gray);
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 50px;
        }

        .benefit-card {
            background-color: var(--white);
            border-radius: 10px;
            box-shadow: var(--shadow);
            padding: 30px;
            text-align: center;
            transition: var(--transition);
        }

        .benefit-card:hover {
            transform: translateY(-5px);
        }

        .benefit-icon {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 20px;
        }

        .faq-section {
            padding: 80px 0;
        }

        .faq-container {
            max-width: 800px;
            margin: 50px auto 0;
        }

        .faq-item {
            margin-bottom: 20px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .faq-question {
            background-color: var(--white);
            padding: 20px;
            cursor: pointer;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .faq-question i {
            transition: var(--transition);
        }

        .faq-item.active .faq-question i {
            transform: rotate(180deg);
        }

        .faq-answer {
            background-color: var(--light-gray);
            padding: 0 20px;
            max-height: 0;
            overflow: hidden;
            transition: var(--transition);
        }

        .faq-item.active .faq-answer {
            padding: 20px;
            max-height: 1000px;
        }

        .comparison-section {
            padding: 80px 0;
            background-color: var(--light-gray);
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 50px;
            background-color: var(--white);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .comparison-table th, .comparison-table td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }

        .comparison-table th {
            background-color: var(--primary-color);
            color: var(--white);
            font-weight: 600;
        }

        .comparison-table tr:last-child td {
            border-bottom: none;
        }

        .comparison-table td:first-child {
            text-align: left;
            font-weight: 500;
        }

        .comparison-check {
            color: var(--primary-color);
            font-size: 1.2rem;
        }

        .comparison-times {
            color: #ccc;
            font-size: 1.2rem;
        }

        @media (max-width: 992px) {
            .pricing-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .benefits-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .comparison-table {
                display: block;
                overflow-x: auto;
            }
        }

        @media (max-width: 768px) {
            .pricing-grid, .benefits-grid {
                grid-template-columns: 1fr;
            }

            .pricing-toggle {
                flex-direction: column;
            }

            .toggle-label {
                margin: 10px 0;
            }

            .annual-discount {
                margin-left: 0;
                margin-top: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.html" class="logo">
                    <img src="img/logo.png" alt="Growy NET logo">
                </a>
                <div class="menu-toggle" id="mobile-menu">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
                <ul class="nav-menu">
                    <li><a href="index.html" class="nav-link">Domů</a></li>
                    <li><a href="produkt.html" class="nav-link">Produkt</a></li>
                    <li><a href="moduly.html" class="nav-link">Moduly</a></li>
                    <li><a href="pro-koho.html" class="nav-link">Pro koho</a></li>
                    <li><a href="cena.html" class="nav-link active">Cena</a></li>
                    <li><a href="reference.html" class="nav-link">Reference</a></li>
                    <li><a href="blog.html" class="nav-link">Blog</a></li>
                    <li><a href="kontakt.html" class="nav-link">Kontakt</a></li>
                </ul>
                <a href="vyzkouset-zdarma.html" class="btn btn-primary">Vyzkoušet zdarma</a>
            </nav>
        </div>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1>Transparentní cenová politika</h1>
            <p>Vyberte si cenový plán, který nejlépe odpovídá potřebám vaší společnosti. Žádné skryté poplatky, žádné závazky.</p>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="pricing-section">
        <div class="container">
            <h2>Cenové plány</h2>
            <p class="section-header">Vyberte si plán, který nejlépe vyhovuje vašim potřebám. Všechny plány zahrnují přístup k webovému rozhraní, mobilní aplikaci a základní podporu.</p>

            <div class="pricing-toggle">
                <span class="toggle-label">Měsíčně</span>
                <label class="toggle-switch">
                    <input type="checkbox" id="pricing-toggle">
                    <span class="toggle-slider"></span>
                </label>
                <span class="toggle-label">Ročně</span>
                <span class="annual-discount">Ušetříte 20%</span>
            </div>

            <div class="pricing-grid">
                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3>Start</h3>
                        <div class="pricing-price" data-monthly="49" data-annual="39">
                            od 49 Kč <span>uživatel / měsíc</span>
                        </div>
                        <p>Začněte se základními moduly</p>
                    </div>
                    <div class="pricing-content">
                        <ul class="pricing-features">
                            <li>Balíček základních modulů (Kolegové, Chat, Novinky, Události, Ankety, Důležitá sdělení, Galerie, Rozcestník, FAQ, O firmě)</li>
                            <li>Webové administrační rozhraní aplikace</li>
                            <li>Multiplatformní prostředí pro koncové uživatele (Web, Android app, iOS app)</li>
                            <li>Provoz v cloudu bez nutnosti instalace na vlastní server</li>
                            <li>Přizpůsobení vzhledu aplikace dle Vašeho brandu</li>
                            <li>API rozhraní</li>
                            <li>Základní podpora</li>
                        </ul>
                        <div class="pricing-button">
                            <a href="vyzkouset-zdarma.html" class="btn btn-primary">Vyzkoušet zdarma</a>
                        </div>
                    </div>
                </div>

                <div class="pricing-card popular">
                    <div class="pricing-header">
                        <h3>Plus</h3>
                        <div class="pricing-price" data-monthly="79" data-annual="63">
                            od 79 Kč <span>uživatel / měsíc</span>
                        </div>
                        <p>Přidejte další moduly dle potřeby</p>
                    </div>
                    <div class="pricing-content">
                        <ul class="pricing-features">
                            <li>Vše z balíčku Start</li>
                            <li>Výběr z dalších modulů (Firemní časopis, Směrnice, Schránka důvěry, Rezervace, Jídelníček, Benefity, Helpdesk, Nástěnka, Knihovna dokumentů)</li>
                            <li>Možnost přidání až 5 modulů z nabídky Plus</li>
                            <li>Prioritní podpora</li>
                            <li>Školení administrátorů</li>
                            <li>Pravidelné aktualizace a nové funkce</li>
                            <li>Pokročilé statistiky a reporty</li>
                        </ul>
                        <div class="pricing-button">
                            <a href="vyzkouset-zdarma.html" class="btn btn-primary">Vyzkoušet zdarma</a>
                        </div>
                    </div>
                </div>

                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3>Enterprise</h3>
                        <div class="pricing-price">
                            Individuální <span>dle potřeb</span>
                        </div>
                        <p>Kompletní řešení na míru</p>
                    </div>
                    <div class="pricing-content">
                        <ul class="pricing-features">
                            <li>Vše z balíčku Plus</li>
                            <li>Neomezený počet modulů</li>
                            <li>Vývoj modulů na míru</li>
                            <li>Integrace s vašimi firemními systémy</li>
                            <li>Dedikovaný account manager</li>
                            <li>SLA s garantovanou dostupností</li>
                            <li>Možnost on-premise řešení</li>
                            <li>Pokročilé zabezpečení a compliance</li>
                            <li>Prémiová podpora 24/7</li>
                        </ul>
                        <div class="pricing-button">
                            <a href="vyzkouset-zdarma.html" class="btn btn-primary">Kontaktujte nás</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Comparison Section -->
    <section class="comparison-section">
        <div class="container">
            <h2>Porovnání plánů</h2>
            <p class="section-header">Podrobné srovnání všech funkcí a modulů v jednotlivých cenových plánech.</p>

            <div class="table-responsive">
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>Funkce</th>
                            <th>Start</th>
                            <th>Plus</th>
                            <th>Enterprise</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Základní moduly</td>
                            <td><i class="fas fa-check comparison-check"></i></td>
                            <td><i class="fas fa-check comparison-check"></i></td>
                            <td><i class="fas fa-check comparison-check"></i></td>
                        </tr>
                        <tr>
                            <td>Mobilní aplikace (iOS, Android)</td>
                            <td><i class="fas fa-check comparison-check"></i></td>
                            <td><i class="fas fa-check comparison-check"></i></td>
                            <td><i class="fas fa-check comparison-check"></i></td>
                        </tr>
                        <tr>
                            <td>Webové rozhraní</td>
                            <td><i class="fas fa-check comparison-check"></i></td>
                            <td><i class="fas fa-check comparison-check"></i></td>
                            <td><i class="fas fa-check comparison-check"></i></td>
                        </tr>
                        <tr>
                            <td>Přizpůsobení vzhledu</td>
                            <td><i class="fas fa-check comparison-check"></i></td>
                            <td><i class="fas fa-check comparison-check"></i></td>
                            <td><i class="fas fa-check comparison-check"></i></td>
                        </tr>
                        <tr>
                            <td>Plus moduly</td>
                            <td><i class="fas fa-times comparison-times"></i></td>
                            <td>Max. 5 modulů</td>
                            <td>Neomezeně</td>
                        </tr>
                        <tr>
                            <td>Moduly na míru</td>
                            <td><i class="fas fa-times comparison-times"></i></td>
                            <td><i class="fas fa-times comparison-times"></i></td>
                            <td><i class="fas fa-check comparison-check"></i></td>
                        </tr>
                        <tr>
                            <td>Integrace s firemními systémy</td>
                            <td>Základní API</td>
                            <td>Rozšířené API</td>
                            <td>Plná integrace</td>
                        </tr>
                        <tr>
                            <td>Zákaznická podpora</td>
                            <td>E-mail</td>
                            <td>E-mail, telefon</td>
                            <td>Dedikovaný tým</td>
                        </tr>
                        <tr>
                            <td>SLA</td>
                            <td><i class="fas fa-times comparison-times"></i></td>
                            <td>99.5% dostupnost</td>
                            <td>99.9% dostupnost</td>
                        </tr>
                        <tr>
                            <td>Školení</td>
                            <td>Online dokumentace</td>
                            <td>Základní školení</td>
                            <td>Komplexní školení</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </section>

    <!-- Benefits Section -->
    <section class="benefits-section">
        <div class="container">
            <h2>Výhody Growy NET</h2>
            <p class="section-header">Proč si vybrat Growy NET pro vaši interní komunikaci a digitalizaci firemních procesů?</p>

            <div class="benefits-grid">
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <div class="benefit-content">
                        <h3>Rychlé nasazení</h3>
                        <p>Aplikaci můžete začít používat během několika dní bez nutnosti složité implementace.</p>
                    </div>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-puzzle-piece"></i>
                    </div>
                    <div class="benefit-content">
                        <h3>Modulární řešení</h3>
                        <p>Vyberte si pouze ty moduly, které skutečně potřebujete, a plaťte jen za to, co využíváte.</p>
                    </div>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="benefit-content">
                        <h3>Pravidelné aktualizace</h3>
                        <p>Neustále pracujeme na vylepšování aplikace a přidávání nových funkcí, které automaticky získáváte v rámci předplatného.</p>
                    </div>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="benefit-content">
                        <h3>Bezpečnost dat</h3>
                        <p>Vaše data jsou v bezpečí díky šifrovaným přenosům, pravidelným zálohám a plnému souladu s GDPR.</p>
                    </div>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="benefit-content">
                        <h3>Profesionální podpora</h3>
                        <p>Náš tým je vám k dispozici každý pracovní den od 8:00 do 16:00 a pomůže vám s jakýmkoliv problémem nebo dotazem.</p>
                    </div>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="benefit-content">
                        <h3>Multiplatformní řešení</h3>
                        <p>Aplikace je dostupná na webu, iOS a Android, takže k ní máte přístup kdykoliv a kdekoliv.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section">
        <div class="container">
            <h2>Často kladené dotazy</h2>

            <div class="faq-container">
                <div class="faq-item">
                    <div class="faq-question">
                        Jak zjistím přesnou cenu za uživatele?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Kontaktujte nás a obratem se Vám ozve náš obchodní zástupce, který Vám kromě cenových podmínek nabídne také zkušební verzi aplikace zdarma. Cena se odvíjí od počtu uživatelů a vybraných modulů.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">
                        Mohu změnit tarif v průběhu používání?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Ano, tarif můžete kdykoliv změnit. Pokud přejdete na vyšší tarif, změna se projeví okamžitě. Při přechodu na nižší tarif se změna projeví na konci aktuálního fakturačního období.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">
                        Jak dlouho trvá implementace?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Základní implementace trvá 1-2 týdny. U větších společností nebo při požadavku na integraci s jinými systémy může být doba implementace delší. Přesný harmonogram vám sdělíme po úvodní konzultaci.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">
                        Poskytujete školení pro administrátory?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Ano, v rámci tarifů Plus a Enterprise poskytujeme školení pro administrátory. U tarifu Start nabízíme online dokumentaci a video návody.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">
                        Jak je to s ochranou osobních údajů?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Growy NET je plně v souladu s GDPR. Všechna data jsou uložena na serverech v EU a přenosy jsou šifrovány. Jako správce osobních údajů máte plnou kontrolu nad tím, jaká data jsou v aplikaci ukládána.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
        <div class="container">
            <h2>Připraveni začít?</h2>
            <p>Vyzkoušejte Growy NET zdarma a objevte, jak může zlepšit interní komunikaci ve vaší společnosti.</p>
            <a href="vyzkouset-zdarma.html" class="btn btn-primary">Vyzkoušet zdarma</a>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-top">
                <div class="footer-logo">
                    <img src="img/logo.png" alt="Growy NET logo">
                    <p>Flexibilní zaměstnanecká aplikace</p>
                    <div class="social-links">
                        <a href="https://www.linkedin.com/company/growy-net" target="_blank"><i class="fab fa-linkedin"></i></a>
                        <a href="https://www.facebook.com/growynet.cz/" target="_blank"><i class="fab fa-facebook"></i></a>
                        <a href="https://www.instagram.com/growy_net/" target="_blank"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h3>Stránky</h3>
                    <ul>
                        <li><a href="produkt.html">Produkt</a></li>
                        <li><a href="moduly.html">Moduly</a></li>
                        <li><a href="cena.html">Cena</a></li>
                        <li><a href="kontakt.html">Kontakt</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Kontakt</h3>
                    <p><strong>growy s.r.o.</strong></p>
                    <p>Na Folimance 2155/15, Praha, 120 00</p>
                    <p>+420 702 040 289, <EMAIL></p>
                    <p>IČ: 09477454, DIČ: CZ09477454</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>Copyright © 2025 Growy NET | Powered by Growy</p>
                <div class="footer-legal">
                    <a href="podminky-uzivani.html">Podmínky užívání</a>
                    <a href="zasady-zpracovani-osobnich-udaju.html">Zásady zpracování osobních údajů</a>
                    <a href="zasady-cookies.html">Zásady cookies (EU)</a>
                </div>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script>
        // Toggle FAQ items
        const faqItems = document.querySelectorAll('.faq-item');
        faqItems.forEach(item => {
            const question = item.querySelector('.faq-question');
            question.addEventListener('click', () => {
                item.classList.toggle('active');
            });
        });

        // Toggle pricing (monthly/annual)
        const pricingToggle = document.getElementById('pricing-toggle');
        const pricingPrices = document.querySelectorAll('.pricing-price');

        pricingToggle.addEventListener('change', () => {
            const isAnnual = pricingToggle.checked;

            pricingPrices.forEach(price => {
                const monthly = price.getAttribute('data-monthly');
                const annual = price.getAttribute('data-annual');

                if (monthly && annual) {
                    if (isAnnual) {
                        price.innerHTML = `od ${annual} Kč <span>uživatel / měsíc</span>`;
                    } else {
                        price.innerHTML = `od ${monthly} Kč <span>uživatel / měsíc</span>`;
                    }
                }
            });
        });
    </script>