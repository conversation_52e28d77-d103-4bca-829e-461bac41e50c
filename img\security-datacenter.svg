<?xml version="1.0" encoding="UTF-8"?>
<svg width="120px" height="120px" viewBox="0 0 120 120" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Fyzické zabezpečení</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="securityGradient">
            <stop stop-color="#349968" offset="0%"></stop>
            <stop stop-color="#0D5533" offset="100%"></stop>
        </linearGradient>
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur in="SourceAlpha" stdDeviation="2"></feGaussianBlur>
            <feOffset dx="0" dy="2" result="offsetblur"></feOffset>
            <feComponentTransfer>
                <feFuncA type="linear" slope="0.3"></feFuncA>
            </feComponentTransfer>
            <feMerge>
                <feMergeNode></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Server Rack Base -->
        <rect fill="#E0E0E0" x="30" y="25" width="60" height="70" rx="2" filter="url(#shadow)"></rect>
        
        <!-- Server Units -->
        <rect fill="url(#securityGradient)" x="35" y="30" width="50" height="10" rx="1"></rect>
        <rect fill="#FFFFFF" x="40" y="33" width="5" height="4" rx="1"></rect>
        <rect fill="#FFFFFF" x="50" y="33" width="5" height="4" rx="1"></rect>
        <circle fill="#FFFFFF" cx="75" cy="35" r="2"></circle>
        
        <rect fill="url(#securityGradient)" x="35" y="45" width="50" height="10" rx="1"></rect>
        <rect fill="#FFFFFF" x="40" y="48" width="5" height="4" rx="1"></rect>
        <rect fill="#FFFFFF" x="50" y="48" width="5" height="4" rx="1"></rect>
        <circle fill="#FFFFFF" cx="75" cy="50" r="2"></circle>
        
        <rect fill="url(#securityGradient)" x="35" y="60" width="50" height="10" rx="1"></rect>
        <rect fill="#FFFFFF" x="40" y="63" width="5" height="4" rx="1"></rect>
        <rect fill="#FFFFFF" x="50" y="63" width="5" height="4" rx="1"></rect>
        <circle fill="#FFFFFF" cx="75" cy="65" r="2"></circle>
        
        <rect fill="url(#securityGradient)" x="35" y="75" width="50" height="10" rx="1"></rect>
        <rect fill="#FFFFFF" x="40" y="78" width="5" height="4" rx="1"></rect>
        <rect fill="#FFFFFF" x="50" y="78" width="5" height="4" rx="1"></rect>
        <circle fill="#FFFFFF" cx="75" cy="80" r="2"></circle>
        
        <!-- Security Elements -->
        <circle fill="#FFFFFF" cx="90" cy="30" r="12" filter="url(#shadow)"></circle>
        <circle fill="url(#securityGradient)" cx="90" cy="30" r="8"></circle>
        <path d="M90,26 L90,30 L94,30" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        
        <!-- Security Camera -->
        <path d="M15,25 C15,20 20,20 20,25 L20,35 C20,40 15,40 15,35 Z" fill="url(#securityGradient)" filter="url(#shadow)"></path>
        <circle fill="#FFFFFF" cx="17.5" cy="30" r="1.5"></circle>
        <path d="M15,30 L10,35" stroke="url(#securityGradient)" stroke-width="2" stroke-linecap="round"></path>
        
        <!-- Secure Door -->
        <rect fill="#CCCCCC" x="85" y="60" width="20" height="35" rx="2" filter="url(#shadow)"></rect>
        <rect fill="#FFFFFF" x="87" y="65" width="16" height="25" rx="1"></rect>
        <circle fill="url(#securityGradient)" cx="95" cy="80" r="3"></circle>
        <rect fill="url(#securityGradient)" x="94" y="75" width="2" height="5" rx="1"></rect>
        
        <!-- Floor -->
        <rect fill="#E0E0E0" x="10" y="95" width="100" height="5" rx="2"></rect>
    </g>
</svg>
