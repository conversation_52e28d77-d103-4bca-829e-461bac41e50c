<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> - <PERSON>rowy NET</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f0f0f0;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        h1 {
            color: #349968;
            margin-bottom: 30px;
        }
        
        .device-selector {
            margin-bottom: 20px;
        }
        
        select, button {
            padding: 8px 15px;
            margin: 0 5px;
            border-radius: 5px;
            border: 1px solid #ccc;
        }
        
        button {
            background-color: #349968;
            color: white;
            border: none;
            cursor: pointer;
        }
        
        button:hover {
            background-color: #0D5533;
        }
        
        .device-container {
            position: relative;
            margin: 20px auto;
            transition: all 0.3s ease;
        }
        
        .device-frame {
            position: relative;
            background-color: #111;
            border-radius: 40px;
            padding: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        
        .device-screen {
            border-radius: 30px;
            overflow: hidden;
            background-color: white;
            transition: all 0.3s ease;
        }
        
        iframe {
            border: none;
            width: 100%;
            height: 100%;
            transition: all 0.3s ease;
        }
        
        .device-home {
            position: absolute;
            bottom: 5px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 5px;
            background-color: #444;
            border-radius: 3px;
        }
        
        .device-info {
            margin-top: 20px;
            color: #666;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>Mobilní náhled webu Growy NET</h1>
    
    <div class="device-selector">
        <select id="device-select">
            <option value="iphone12">iPhone 12/13 (390x844)</option>
            <option value="iphone8">iPhone 8 (375x667)</option>
            <option value="galaxys20">Galaxy S20 (360x800)</option>
            <option value="pixel5">Pixel 5 (393x851)</option>
        </select>
        <button id="rotate-btn">Otočit zařízení</button>
        <button id="reload-btn">Obnovit stránku</button>
    </div>
    
    <div class="device-container">
        <div class="device-frame">
            <div class="device-screen">
                <iframe id="mobile-frame" src="produkt.html"></iframe>
            </div>
            <div class="device-home"></div>
        </div>
    </div>
    
    <div class="device-info">
        <p id="device-dimensions">Rozměry: 390 x 844 px</p>
    </div>
    
    <script>
        const deviceSelect = document.getElementById('device-select');
        const rotateBtn = document.getElementById('rotate-btn');
        const reloadBtn = document.getElementById('reload-btn');
        const mobileFrame = document.getElementById('mobile-frame');
        const deviceFrame = document.querySelector('.device-frame');
        const deviceScreen = document.querySelector('.device-screen');
        const deviceInfo = document.getElementById('device-dimensions');
        
        let isLandscape = false;
        
        const devices = {
            iphone12: { width: 390, height: 844 },
            iphone8: { width: 375, height: 667 },
            galaxys20: { width: 360, height: 800 },
            pixel5: { width: 393, height: 851 }
        };
        
        function updateDeviceSize() {
            const device = devices[deviceSelect.value];
            let width = device.width;
            let height = device.height;
            
            if (isLandscape) {
                [width, height] = [height, width];
            }
            
            deviceScreen.style.width = `${width}px`;
            deviceScreen.style.height = `${height}px`;
            
            deviceFrame.style.width = `${width + 20}px`;
            deviceFrame.style.height = `${height + 20}px`;
            
            deviceInfo.textContent = `Rozměry: ${width} x ${height} px`;
        }
        
        deviceSelect.addEventListener('change', updateDeviceSize);
        
        rotateBtn.addEventListener('click', () => {
            isLandscape = !isLandscape;
            updateDeviceSize();
        });
        
        reloadBtn.addEventListener('click', () => {
            mobileFrame.src = mobileFrame.src;
        });
        
        // Inicializace
        updateDeviceSize();
    </script>
</body>
</html>
