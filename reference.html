<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="description" content="Growy NET - Reference a úspěšné případové studie našich klientů">
    <meta name="theme-color" content="#349968">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-TileColor" content="#349968">
    <meta name="msapplication-tap-highlight" content="no">
    <title>Reference - Growy NET | Flexibilní zaměstnanecká aplikace</title>
    <link rel="icon" type="image/svg+xml" href="img/favicon.svg">
    <link rel="stylesheet" href="css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Page Specific Styles */
        .page-header {
            background-color: var(--light-gray);
            padding-top: 150px;
            padding-bottom: 50px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center, rgba(52, 153, 104, 0.05) 0%, rgba(255, 255, 255, 0) 70%);
            z-index: 0;
        }

        .page-header .container {
            position: relative;
            z-index: 1;
        }

        .case-study-section {
            padding: 80px 0;
            position: relative;
        }

        .case-study-section:nth-child(even) {
            background-color: var(--light-gray);
        }

        .case-study-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark), var(--primary-color));
            opacity: 0.2;
        }

        .case-study-container {
            display: flex;
            align-items: center;
            gap: 50px;
            opacity: 1; /* Začínáme s plnou viditelností */
            transform: translateY(0); /* Začínáme bez posunu */
            transition: opacity 0.8s ease, transform 0.8s ease;
        }

        .case-study-container.animated {
            opacity: 1;
            transform: translateY(0);
        }

        .case-study-image {
            flex: 0 0 40%;
            position: relative;
            overflow: hidden;
            border-radius: 15px;
            box-shadow: var(--shadow);
            transition: all 0.5s ease;
        }

        .case-study-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(52, 153, 104, 0.1), rgba(13, 85, 51, 0.3));
            opacity: 0;
            transition: opacity 0.5s ease;
            z-index: 1;
        }

        .case-study-container:hover .case-study-image::before {
            opacity: 1;
        }

        .case-study-container:hover .case-study-image {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(52, 153, 104, 0.2);
        }

        .case-study-image img {
            width: 100%;
            border-radius: 15px;
            transition: transform 0.5s ease;
        }

        .case-study-container:hover .case-study-image img {
            transform: scale(1.05);
        }

        .case-study-content {
            flex: 0 0 60%;
        }

        .case-study-logo {
            max-width: 200px;
            margin-bottom: 20px;
            transition: transform 0.5s ease;
        }

        .case-study-container:hover .case-study-logo {
            transform: translateY(-5px);
        }

        .case-study-stats {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            margin: 30px 0;
        }

        .case-study-stat {
            flex: 1;
            min-width: 150px;
            background-color: var(--white);
            padding: 20px;
            border-radius: 15px;
            box-shadow: var(--shadow);
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .case-study-stat::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
            transform: translateY(-100%);
            transition: transform 0.3s ease;
        }

        .case-study-stat:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(52, 153, 104, 0.15);
        }

        .case-study-stat:hover::before {
            transform: translateY(0);
        }

        .case-study-stat h3 {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 10px;
            position: relative;
            display: inline-block;
        }

        .case-study-stat h3.counter {
            opacity: 0;
            transform: translateY(10px);
            transition: opacity 0.5s ease, transform 0.5s ease;
        }

        .case-study-stat h3.counter.animated {
            opacity: 1;
            transform: translateY(0);
        }

        .case-study-stat h3.pre-animated {
            opacity: 1;
            transform: translateY(0);
        }

        .case-study-quote {
            background-color: var(--white);
            padding: 30px;
            border-radius: 15px;
            box-shadow: var(--shadow);
            margin-top: 30px;
            position: relative;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .case-study-quote::before {
            content: "\201C";
            font-size: 4rem;
            color: var(--primary-color);
            position: absolute;
            top: 10px;
            left: 20px;
            opacity: 0.2;
            transition: all 0.3s ease;
        }

        .case-study-quote::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.5s ease;
        }

        .case-study-quote:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(52, 153, 104, 0.15);
        }

        .case-study-quote:hover::before {
            transform: translateY(-5px) scale(1.1);
            opacity: 0.3;
        }

        .case-study-quote:hover::after {
            transform: scaleX(1);
        }

        .case-study-quote p {
            font-style: italic;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }

        .case-study-quote-author {
            display: flex;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .case-study-quote-author img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-right: 15px;
            border: 3px solid var(--white);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .case-study-quote:hover .case-study-quote-author img {
            transform: scale(1.1);
            box-shadow: 0 8px 20px rgba(52, 153, 104, 0.2);
        }

        .case-study-quote-author-info h4 {
            margin-bottom: 5px;
            transition: color 0.3s ease;
        }

        .case-study-quote:hover .case-study-quote-author-info h4 {
            color: var(--primary-color);
        }

        .case-study-quote-author-info p {
            margin-bottom: 0;
            color: var(--gray);
            font-style: normal;
        }

        .testimonials-section {
            padding: 80px 0;
            background-color: var(--light-gray);
            position: relative;
            overflow: hidden;
        }

        .testimonials-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center, rgba(52, 153, 104, 0.05) 0%, rgba(255, 255, 255, 0) 70%);
            z-index: 0;
        }

        .testimonials-section .container {
            position: relative;
            z-index: 1;
        }

        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            margin-top: 50px;
        }

        .testimonial-card {
            background-color: var(--white);
            padding: 30px;
            border-radius: 15px;
            box-shadow: var(--shadow);
            transition: all 0.5s ease;
            position: relative;
            overflow: hidden;
            opacity: 1; /* Začínáme s plnou viditelností */
            transform: translateY(0); /* Začínáme bez posunu */
        }

        .testimonial-card.animated {
            opacity: 1;
            transform: translateY(0);
        }

        .testimonial-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
            transform: translateY(-100%);
            transition: transform 0.3s ease;
        }

        .testimonial-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(52, 153, 104, 0.15);
        }

        .testimonial-card:hover::before {
            transform: translateY(0);
        }

        .testimonial-content {
            position: relative;
            padding-left: 30px;
            margin-bottom: 20px;
        }

        .testimonial-content:before {
            content: "\201C";
            font-size: 3rem;
            color: var(--primary-color);
            position: absolute;
            top: -10px;
            left: 0;
            opacity: 0.3;
            transition: all 0.3s ease;
        }

        .testimonial-card:hover .testimonial-content:before {
            transform: translateY(-5px);
            opacity: 0.5;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
        }

        .testimonial-author-image {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 15px;
            border: 3px solid var(--white);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .testimonial-card:hover .testimonial-author-image {
            transform: scale(1.1);
            box-shadow: 0 8px 20px rgba(52, 153, 104, 0.2);
        }

        .testimonial-author-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .testimonial-author-info h4 {
            margin-bottom: 5px;
            transition: color 0.3s ease;
        }

        .testimonial-card:hover .testimonial-author-info h4 {
            color: var(--primary-color);
        }

        .testimonial-author-info p {
            margin-bottom: 0;
            color: var(--gray);
        }

        .testimonial-company {
            display: flex;
            align-items: center;
            margin-top: 15px;
        }

        .testimonial-company img {
            height: 30px;
            margin-right: 10px;
            transition: transform 0.3s ease;
        }

        .testimonial-card:hover .testimonial-company img {
            transform: scale(1.1);
        }

        /* Stats Section */
        .stats-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--white);
            padding: 60px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .stats-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(ellipse at center, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
            z-index: 0;
        }

        .stats-section .container {
            position: relative;
            z-index: 1;
        }

        .stats-container {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            max-width: 1000px;
            margin: 40px auto 0;
        }

        .stat-item {
            flex: 0 0 calc(25% - 30px);
            margin: 15px;
            padding: 25px 20px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            transition: all 0.5s ease;
            position: relative;
            overflow: hidden;
            opacity: 1; /* Začínáme s plnou viditelností */
            transform: translateY(0); /* Začínáme bez posunu */
        }

        .stat-item.animated {
            opacity: 1;
            transform: translateY(0);
        }

        .stat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(ellipse at center, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
            opacity: 0;
            transition: opacity 0.5s ease;
            z-index: 0;
        }

        .stat-item:hover {
            transform: translateY(-10px);
            background-color: rgba(255, 255, 255, 0.2);
        }

        .stat-item:hover::before {
            opacity: 1;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            display: inline-block;
            z-index: 1;
            opacity: 1; /* Začínáme s plnou viditelností */
            transform: translateY(0); /* Začínáme bez posunu */
            transition: opacity 0.5s ease, transform 0.5s ease;
        }

        .stat-number.animated {
            opacity: 1;
            transform: translateY(0);
        }

        .stat-number::after {
            content: '+';
            position: absolute;
            top: 0;
            right: -20px;
            font-size: 2rem;
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        /* CTA Section */
        .cta-section {
            background-color: var(--light-gray);
            padding: 80px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .cta-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center, rgba(52, 153, 104, 0.05) 0%, rgba(255, 255, 255, 0) 70%);
            z-index: 0;
        }

        .cta-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: var(--white);
            padding: 50px;
            border-radius: 20px;
            box-shadow: var(--shadow);
            position: relative;
            overflow: hidden;
            z-index: 1;
            opacity: 1; /* Začínáme s plnou viditelností */
            transform: translateY(0); /* Začínáme bez posunu */
            transition: opacity 0.8s ease, transform 0.8s ease;
        }

        .cta-container.animated {
            opacity: 1;
            transform: translateY(0);
        }

        .cta-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.5s ease;
        }

        .cta-container:hover::before {
            transform: scaleX(1);
        }

        .cta-content {
            position: relative;
            z-index: 1;
        }

        .cta-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
        }

        .btn-secondary {
            background-color: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background-color: var(--primary-color);
            color: var(--white);
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(52, 153, 104, 0.2);
        }

        /* Touch device optimizations */
        .touch-active {
            transform: scale(0.98) !important;
            transition: transform 0.1s ease !important;
        }

        /* Disable hover effects on touch devices */
        @media (hover: none) {
            .btn:hover, .nav-link:hover, .case-study-stat:hover,
            .testimonial-card:hover, .case-study-quote:hover {
                transform: none;
                box-shadow: var(--shadow);
            }

            .case-study-stat:hover::before, .testimonial-card:hover::before, .case-study-quote:hover::after {
                transform: scaleX(0);
            }

            .case-study-container:hover .case-study-image {
                transform: none;
            }

            .case-study-container:hover .case-study-image img {
                transform: none;
            }

            .case-study-container:hover .case-study-logo {
                transform: none;
            }

            .case-study-quote:hover .case-study-quote-author img {
                transform: none;
            }

            .testimonial-card:hover .testimonial-author-image {
                transform: none;
            }

            .testimonial-card:hover .testimonial-company img {
                transform: none;
            }
        }

        @media (max-width: 992px) {
            .case-study-container {
                flex-direction: column;
            }

            .case-study-image, .case-study-content {
                flex: 0 0 100%;
            }

            .case-study-image {
                margin-bottom: 30px;
            }

            .testimonials-grid {
                grid-template-columns: 1fr;
            }

            .stat-item {
                flex: 0 0 calc(50% - 30px);
            }
        }

        @media (max-width: 768px) {
            .page-header {
                padding-top: 120px;
                padding-bottom: 40px;
            }

            .case-study-section, .testimonials-section {
                padding: 60px 0;
            }

            .case-study-stat h3 {
                font-size: 2rem;
            }

            .case-study-quote {
                padding: 25px 20px;
            }

            .case-study-quote-author img {
                width: 50px;
                height: 50px;
            }

            .testimonial-card {
                padding: 25px 20px;
            }

            .testimonial-author-image {
                width: 50px;
                height: 50px;
            }

            .stat-item {
                flex: 0 0 calc(100% - 30px);
                padding: 20px 15px;
            }

            .stat-number {
                font-size: 2.5rem;
            }

            .cta-container {
                padding: 40px 25px;
            }

            .cta-buttons {
                flex-direction: column;
                gap: 15px;
            }
        }

        @media (max-width: 480px) {
            .page-header {
                padding-top: 100px;
            }

            h1 {
                font-size: 1.8rem;
            }

            h2 {
                font-size: 1.6rem;
            }

            .case-study-stats {
                flex-direction: column;
                gap: 15px;
            }

            .case-study-quote-author {
                flex-direction: column;
                align-items: flex-start;
            }

            .case-study-quote-author img {
                margin-bottom: 10px;
            }

            .testimonial-author {
                flex-direction: column;
                align-items: flex-start;
            }

            .testimonial-author-image {
                margin-bottom: 10px;
            }

            .cta-container {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.html" class="logo">
                    <img src="img/logo.png" alt="Growy NET logo">
                </a>
                <div class="menu-toggle" id="mobile-menu">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
                <ul class="nav-menu">
                    <li><a href="index.html" class="nav-link">Domů</a></li>
                    <li><a href="produkt.html" class="nav-link">Produkt</a></li>
                    <li><a href="moduly.html" class="nav-link">Moduly</a></li>
                    <li><a href="pro-koho.html" class="nav-link">Pro koho</a></li>
                    <li><a href="cena.html" class="nav-link">Cena</a></li>
                    <li><a href="reference.html" class="nav-link active">Reference</a></li>
                    <li><a href="blog.html" class="nav-link">Blog</a></li>
                    <li><a href="kontakt.html" class="nav-link">Kontakt</a></li>
                </ul>
                <a href="vyzkouset-zdarma.html" class="btn btn-primary">Vyzkoušet zdarma</a>
            </nav>
        </div>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1>Reference a případové studie</h1>
            <p>Podívejte se, jak Growy NET pomáhá firmám zlepšit interní komunikaci a digitalizovat firemní procesy.</p>
        </div>
    </section>

    <!-- Case Study - Drbna -->
    <section class="case-study-section">
        <div class="container">
            <div class="case-study-container">
                <div class="case-study-image">
                    <img src="img/drbna-case-study.jpg" alt="Drbna - případová studie">
                </div>
                <div class="case-study-content">
                    <img src="img/drbna-logo.svg" alt="Drbna logo" class="case-study-logo">
                    <h2>Jak Drbna zlepšila komunikaci mezi redakcemi</h2>
                    <p>Drbna.cz je internetový zpravodajský a publicistický portál společnosti TRIMA NEWS s.r.o., který působí v devíti regionech České republiky. S rostoucím počtem redaktorů a regionálních redakcí bylo stále obtížnější zajistit, aby všichni měli přístup k aktuálním informacím a mohli efektivně spolupracovat.</p>

                    <div class="case-study-stats">
                        <div class="case-study-stat">
                            <h3 class="pre-animated">9</h3>
                            <p>Regionálních redakcí</p>
                        </div>
                        <div class="case-study-stat">
                            <h3 class="pre-animated">20+</h3>
                            <p>Redaktorů</p>
                        </div>
                        <div class="case-study-stat">
                            <h3 class="pre-animated">83%</h3>
                            <p>Důvěryhodnost</p>
                        </div>
                    </div>

                    <h3>Řešení</h3>
                    <p>Implementace Growy NET umožnila Drbně centralizovat veškerou komunikaci a sdílení informací na jedné platformě. Klíčové moduly, které Drbna využívá:</p>
                    <ul>
                        <li>Novinky - pro sdílení aktuálních informací s možností cílení na konkrétní redakce</li>
                        <li>Události - pro plánování a organizaci redakčních porad a školení</li>
                        <li>Směrnice - pro správu a distribuci redakčních postupů a standardů</li>
                        <li>Chat - pro rychlou komunikaci mezi redaktory a týmy</li>
                        <li>Ankety - pro získávání zpětné vazby od redaktorů i čtenářů</li>
                    </ul>

                    <h3>Výsledky</h3>
                    <p>Po implementaci Growy NET zaznamenala Drbna výrazné zlepšení v interní komunikaci a spolupráci mezi redakcemi. Redaktoři mají nyní přístup ke všem důležitým informacím přímo ve svých mobilních telefonech, což výrazně zrychlilo a zefektivnilo komunikaci. Podle dotazníkového šetření z roku 2023 důvěřuje obsahu Drbny 83 % čtenářů.</p>

                    <div class="case-study-quote">
                        <p>S růstem našeho týmu v regionech po celé České republice jsme potřebovali najít nástroj, který by usnadnil komunikaci a předávání informací mezi jednotlivými týmy. Tímto nástrojem se stala aplikace Growy NET, kterou má každý zaměstnanec nainstalovanou ve svém telefonu. Díky ní mají všichni přístup k aktuálním informacím a mohou využívat užitečné moduly, které jim zjednodušují práci.</p>
                        <div class="case-study-quote-author">
                            <img src="img/matej-matousek.jpg" alt="Matěj Matoušek">
                            <div class="case-study-quote-author-info">
                                <h4>Matěj Matoušek</h4>
                                <p>Finanční ředitel, TRIMA NEWS s.r.o.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials-section">
        <div class="container">
            <h2>Co o nás říkají naši klienti</h2>
            <p class="section-header">Přečtěte si, jak Growy NET pomáhá firmám z různých odvětví zlepšit interní komunikaci a digitalizovat firemní procesy.</p>

            <div class="testimonials-grid">
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>Growy NET nám pomohl výrazně zlepšit komunikaci mezi jednotlivými redakcemi v různých regionech. Díky možnosti cílení obsahu na konkrétní skupiny redaktorů se k nim dostávají pouze relevantní informace, což zvyšuje jejich angažovanost a efektivitu práce.</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="testimonial-author-image">
                            <img src="img/testimonial-1.jpg" alt="Michael Daněk">
                        </div>
                        <div class="testimonial-author-info">
                            <h4>Michael Daněk</h4>
                            <p>Šéfredaktor, TRIMA NEWS s.r.o.</p>
                        </div>
                    </div>
                    <div class="testimonial-company">
                        <img src="img/company-1.png" alt="Drbna logo">
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>Díky Growy NET jsme dokázali efektivně propojit všech 9 regionálních redakcí po celé České republice. Oceňujeme zejména možnost sdílení aktuálních informací a koordinaci práce mezi jednotlivými týmy, což výrazně zlepšilo kvalitu našeho zpravodajství.</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="testimonial-author-image">
                            <img src="img/testimonial-2.jpg" alt="Libor Matoušek">
                        </div>
                        <div class="testimonial-author-info">
                            <h4>Libor Matoušek</h4>
                            <p>Výkonný ředitel, TRIMA NEWS s.r.o.</p>
                        </div>
                    </div>
                    <div class="testimonial-company">
                        <img src="img/company-2.png" alt="Drbna logo">
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>Implementace Growy NET nám pomohla digitalizovat mnoho redakčních procesů, které byly dříve řešeny neefektivně. Díky modulům jako Směrnice a Ankety jsme výrazně zlepšili interní komunikaci a získávání zpětné vazby od čtenářů, což se pozitivně odráží na kvalitě našeho obsahu.</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="testimonial-author-image">
                            <img src="img/testimonial-3.jpg" alt="Radek Šuba">
                        </div>
                        <div class="testimonial-author-info">
                            <h4>Radek Šuba</h4>
                            <p>Obchodní ředitel, TRIMA NEWS s.r.o.</p>
                        </div>
                    </div>
                    <div class="testimonial-company">
                        <img src="img/company-3.png" alt="Drbna logo">
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>Díky Growy NET jsme dokázali výrazně zlepšit komunikaci mezi našimi redaktory, kteří často pracují v terénu. Mobilní aplikace jim umožňuje mít vždy aktuální informace po ruce a efektivně spolupracovat s kolegy z jiných regionů, což je pro naši práci naprosto klíčové.</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="testimonial-author-image">
                            <img src="img/testimonial-4.jpg" alt="Matěj Matoušek">
                        </div>
                        <div class="testimonial-author-info">
                            <h4>Matěj Matoušek</h4>
                            <p>Finanční ředitel, TRIMA NEWS s.r.o.</p>
                        </div>
                    </div>
                    <div class="testimonial-company">
                        <img src="img/company-4.png" alt="Drbna logo">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
        <div class="container">
            <h2>Zaujalo Vás naše řešení?</h2>
            <p>Kontaktujte nás a vyzkoušejte aplikaci na vlastní kůži.</p>
            <a href="vyzkouset-zdarma.html" class="btn btn-primary">Vyzkoušet zdarma</a>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-top">
                <div class="footer-logo">
                    <img src="img/logo.png" alt="Growy NET logo">
                    <p>Flexibilní zaměstnanecká aplikace</p>
                    <div class="social-links">
                        <a href="https://www.linkedin.com/company/growy-net" target="_blank"><i class="fab fa-linkedin"></i></a>
                        <a href="https://www.facebook.com/growynet.cz/" target="_blank"><i class="fab fa-facebook"></i></a>
                        <a href="https://www.instagram.com/growy_net/" target="_blank"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h3>Stránky</h3>
                    <ul>
                        <li><a href="produkt.html">Produkt</a></li>
                        <li><a href="moduly.html">Moduly</a></li>
                        <li><a href="cena.html">Cena</a></li>
                        <li><a href="kontakt.html">Kontakt</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Kontakt</h3>
                    <p><strong>growy s.r.o.</strong></p>
                    <p>Na Folimance 2155/15, Praha, 120 00</p>
                    <p>+420 702 040 289, <EMAIL></p>
                    <p>IČ: 09477454, DIČ: CZ09477454</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>Copyright © 2025 Growy NET | Powered by Growy</p>
                <div class="footer-legal">
                    <a href="podminky-uzivani.html">Podmínky užívání</a>
                    <a href="zasady-zpracovani-osobnich-udaju.html">Zásady zpracování osobních údajů</a>
                    <a href="zasady-cookies.html">Zásady cookies (EU)</a>
                </div>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script src="js/animations.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Animace pro case-study-container
            const caseStudyContainers = document.querySelectorAll('.case-study-container');

            function animateCaseStudies() {
                caseStudyContainers.forEach(container => {
                    if (isInViewport(container)) {
                        container.classList.add('animated');

                        // Animace pro counter v case-study-stat (pouze pro ty, které nemají třídu pre-animated)
                        const counters = container.querySelectorAll('.case-study-stat h3:not(.pre-animated)');
                        counters.forEach(counter => {
                            if (!counter.classList.contains('counter')) {
                                counter.classList.add('counter');
                                const target = parseInt(counter.textContent);
                                const duration = 2000; // 2 sekundy
                                const step = target / duration * 10;
                                let current = 0;

                                counter.textContent = '0';

                                setTimeout(() => {
                                    counter.classList.add('animated');

                                    const updateCounter = () => {
                                        current += step;
                                        if (current < target) {
                                            counter.textContent = Math.floor(current);
                                            requestAnimationFrame(updateCounter);
                                        } else {
                                            counter.textContent = target;
                                        }
                                    };

                                    updateCounter();
                                }, 500);
                            }
                        });
                    }
                });
            }

            // Animace pro testimonial-card
            const testimonialCards = document.querySelectorAll('.testimonial-card');
            let delay = 0;

            function animateTestimonials() {
                testimonialCards.forEach((card, index) => {
                    if (isInViewport(card)) {
                        // Přidáme třídu animated ihned pro všechny karty
                        card.classList.add('animated');
                    }
                });
            }

            // Animace pro CTA sekci
            const ctaContainer = document.querySelector('.cta .container');

            function animateCta() {
                if (ctaContainer && isInViewport(ctaContainer)) {
                    ctaContainer.classList.add('animated');
                }
            }

            // Funkce pro kontrolu, zda je element v dohledu
            function isInViewport(element) {
                const rect = element.getBoundingClientRect();
                return (
                    rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8 &&
                    rect.bottom >= 0
                );
            }

            // Spustit animace při načtení stránky a při scrollování
            animateCaseStudies();
            animateTestimonials();
            animateCta();

            window.addEventListener('scroll', function() {
                animateCaseStudies();
                animateTestimonials();
                animateCta();
            });

            // Přidání třídy counter pro statistiky (pouze pro ty, které nemají třídu pre-animated)
            document.querySelectorAll('.case-study-stat h3:not(.pre-animated)').forEach(stat => {
                if (!stat.classList.contains('counter')) {
                    stat.classList.add('counter');
                }
            });

            // Optimalizace pro dotykové ovládání
            const touchElements = document.querySelectorAll('.btn, .nav-link, .case-study-stat, .testimonial-card, .case-study-quote');

            touchElements.forEach(element => {
                element.addEventListener('touchstart', function() {
                    this.classList.add('touch-active');
                }, { passive: true });

                element.addEventListener('touchend', function() {
                    this.classList.remove('touch-active');
                }, { passive: true });
            });
        });
    </script>
</body>
</html>
