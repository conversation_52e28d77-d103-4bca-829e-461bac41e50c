<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Growy NET - Flexibilní zaměstnanecká aplikace pro interní komunikaci a digitalizaci firemních procesů">
    <title>Cena - Growy NET | Flexibilní zaměstnanecká aplikace</title>
    <link rel="icon" type="image/svg+xml" href="img/favicon.svg">
    <link rel="stylesheet" href="css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Page Specific Styles */
        .page-header {
            background-color: var(--light-gray);
            padding-top: 150px;
            padding-bottom: 80px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .page-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, rgba(52,153,104,0) 0%, rgba(52,153,104,1) 50%, rgba(52,153,104,0) 100%);
        }

        .page-header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            color: var(--dark);
            font-weight: 700;
        }

        .page-header p {
            font-size: 1.2rem;
            max-width: 800px;
            margin: 0 auto;
            color: var(--gray);
            line-height: 1.6;
        }

        .pricing-section {
            padding: 100px 0 80px;
            position: relative;
        }

        .price-highlight {
            background-color: rgba(52, 153, 104, 0.08);
            border-radius: 50px;
            padding: 15px 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            max-width: 600px;
            margin: 30px auto 0;
            box-shadow: 0 3px 15px rgba(52, 153, 104, 0.15);
            border: 1px solid rgba(52, 153, 104, 0.1);
        }

        .price-highlight-icon {
            background-color: var(--primary-color);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.2rem;
            flex-shrink: 0;
        }

        .price-highlight p {
            margin: 0;
            font-size: 1.1rem;
            color: var(--primary-dark);
        }

        .pricing-section h2, .benefits-section h2, .faq-section h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: var(--dark);
            font-weight: 700;
        }

        .section-header {
            text-align: center;
            max-width: 700px;
            margin: 0 auto 50px;
            color: var(--gray);
            font-size: 1.2rem;
            line-height: 1.6;
        }

        /* Price Calculator */
        .price-calculator {
            max-width: 1000px;
            margin: 80px auto 50px;
            background-color: #fcfcfc;
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.08);
            padding: 40px;
            border: 1px solid rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
        }

        .price-calculator::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
        }

        .price-calculator::after {
            content: '';
            position: absolute;
            top: -100px;
            right: -100px;
            width: 200px;
            height: 200px;
            background-color: rgba(52, 153, 104, 0.03);
            border-radius: 50%;
            z-index: 0;
        }

        .price-calculator h3 {
            text-align: center;
            margin-bottom: 30px;
            color: var(--dark);
            font-size: 1.8rem;
            font-weight: 700;
            position: relative;
        }

        .price-calculator h3::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background-color: var(--primary-color);
            border-radius: 3px;
        }

        .calculator-form {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .calculator-field {
            flex: 1;
            min-width: 250px;
        }

        .calculator-field label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: var(--dark);
            font-size: 1.1rem;
        }

        .calculator-field input,
        .calculator-field select {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid rgba(0,0,0,0.08);
            border-radius: 50px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            background-color: #fff;
            box-shadow: 0 3px 10px rgba(0,0,0,0.03);
        }

        .calculator-field input:focus,
        .calculator-field select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(52, 153, 104, 0.15);
        }

        .calculator-result {
            flex: 1 0 100%;
            background: linear-gradient(135deg, rgba(52, 153, 104, 0.08) 0%, rgba(13, 85, 51, 0.08) 100%);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            margin-top: 30px;
            border: 1px solid rgba(52, 153, 104, 0.1);
            box-shadow: 0 5px 20px rgba(52, 153, 104, 0.1);
            position: relative;
        }

        .result-label {
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .result-value {
            font-size: 3.2rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.08);
            background: -webkit-linear-gradient(var(--primary-color), var(--primary-dark));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .result-note {
            font-size: 1rem;
            color: var(--gray);
            max-width: 500px;
            margin: 0 auto 15px;
            line-height: 1.5;
        }

        .coffee-price-note {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            padding: 15px 20px;
            background-color: rgba(52, 153, 104, 0.05);
            border-radius: 50px;
            border: 1px dashed rgba(52, 153, 104, 0.2);
        }

        .coffee-icon {
            background-color: var(--primary-color);
            color: white;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.1rem;
            flex-shrink: 0;
            box-shadow: 0 3px 8px rgba(52, 153, 104, 0.2);
        }

        .coffee-price-note p {
            margin: 0;
            font-size: 1rem;
            color: var(--primary-dark);
            line-height: 1.4;
        }

        .volume-discount {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 15px;
            font-size: 0.9rem;
            color: var(--primary-dark);
            padding: 0 10px;
        }

        .volume-discount i {
            margin-right: 8px;
            color: var(--primary-color);
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 50px;
        }

        .pricing-card {
            background-color: var(--white);
            border-radius: 20px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            overflow: hidden;
            transition: all 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
            position: relative;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .pricing-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.12);
        }

        .pricing-card.popular {
            transform: scale(1.05);
            z-index: 1;
            border: 2px solid var(--primary-color);
            box-shadow: 0 8px 25px rgba(13, 85, 51, 0.25);
            position: relative;
        }

        .pricing-card.popular::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border-radius: 25px;
            z-index: -1;
            opacity: 0.1;
            filter: blur(8px);
        }

        .pricing-card.popular:hover {
            transform: scale(1.05) translateY(-8px);
        }

        .popular-badge {
            position: absolute;
            top: 15px;
            right: 0;
            background-color: #ffb100;
            color: white;
            padding: 5px 15px;
            border-radius: 20px 0 0 20px;
            font-size: 0.8rem;
            font-weight: 600;
            box-shadow: 0 3px 10px rgba(255, 177, 0, 0.3);
            z-index: 2;
        }

        .pricing-header {
            background-color: var(--primary-color);
            color: var(--white);
            padding: 30px 25px;
            text-align: center;
            position: relative;
            border-radius: 20px 20px 0 0;
        }

        .pricing-card:nth-child(1) .pricing-header,
        .pricing-card:nth-child(3) .pricing-header {
            background-color: var(--primary-color);
            background-image: linear-gradient(135deg, var(--primary-color) 0%, #40b37e 100%);
        }

        .pricing-card:nth-child(2) .pricing-header {
            background-color: var(--primary-dark);
            background-image: linear-gradient(135deg, var(--primary-dark) 0%, #0a4428 100%);
        }

        .pricing-icon {
            font-size: 1.1rem;
            margin-bottom: 10px;
            display: inline-block;
            background-color: rgba(255, 255, 255, 0.2);
            width: 32px;
            height: 32px;
            line-height: 32px;
            border-radius: 50%;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .pricing-header h3 {
            margin-bottom: 0;
            font-size: 1.1rem;
            font-weight: 600;
            letter-spacing: -0.5px;
            text-transform: uppercase;
        }

        .pricing-header .pricing-large-text {
            font-size: 2.2rem;
            font-weight: 700;
            line-height: 1;
            margin: 10px 0 5px;
        }

        .pricing-price {
            font-size: 3.5rem;
            font-weight: 800;
            line-height: 1;
            display: block;
            margin-top: 10px;
        }

        .pricing-currency {
            font-size: 3.5rem;
            font-weight: 800;
            margin-left: 5px;
        }

        .pricing-price-note {
            font-size: 0.8rem;
            opacity: 0.9;
            margin-top: 2px;
            font-weight: 400;
            display: block;
            text-align: right;
        }

        .pricing-content {
            padding: 35px 30px 30px;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            background-color: #fcfcfc;
        }

        .pricing-subtitle {
            text-align: center;
            margin-bottom: 25px;
            color: var(--gray);
            font-size: 1.1rem;
        }

        .pricing-features {
            margin-bottom: 30px;
        }

        .pricing-features li {
            margin-bottom: 18px;
            position: relative;
            padding-left: 35px;
            line-height: 1.5;
            color: var(--dark);
        }

        .pricing-features li a {
            color: var(--dark);
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-block;
            position: relative;
        }

        .pricing-features li a:hover {
            color: var(--primary-color);
        }

        .pricing-features li a::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 1px;
            background-color: var(--primary-color);
            transition: width 0.3s ease;
        }

        .pricing-features li a:hover::after {
            width: 100%;
        }

        .pricing-features li a i {
            margin-left: 5px;
            font-size: 0.8rem;
            opacity: 0;
            transform: translateX(-5px);
            transition: all 0.3s ease;
        }

        .pricing-features li a:hover i {
            opacity: 1;
            transform: translateX(0);
        }

        .pricing-features li:last-child {
            margin-bottom: 0;
        }

        .pricing-features li:before {
            content: "\f00c";
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            color: var(--primary-color);
            position: absolute;
            left: 0;
            background-color: rgba(52, 153, 104, 0.1);
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            box-shadow: 0 2px 5px rgba(52, 153, 104, 0.15);
        }

        .pricing-features-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--primary-dark);
            font-size: 1.1rem;
            position: relative;
            display: inline-block;
        }

        .pricing-features-title:after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 40px;
            height: 2px;
            background-color: var(--primary-color);
        }

        .pricing-cta {
            text-align: center;
            margin-top: 25px;
        }

        .pricing-cta .btn {
            width: 100%;
            padding: 14px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
            border-radius: 50px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .pricing-cta .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
            z-index: -1;
        }

        .pricing-cta .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(52, 153, 104, 0.3);
        }

        .pricing-cta .btn:hover::before {
            transform: translateX(100%);
        }

        .pricing-card.popular .pricing-cta .btn {
            background-color: var(--primary-dark);
            box-shadow: 0 4px 10px rgba(13, 85, 51, 0.25);
        }

        .benefits-section {
            padding: 100px 0;
            background-color: var(--light-gray);
            position: relative;
        }

        .benefits-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('data:image/svg+xml;utf8,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" fill="rgba(52,153,104,0.03)"/></svg>');
            background-repeat: repeat;
            z-index: 0;
        }

        .benefits-section .container {
            position: relative;
            z-index: 1;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 50px;
        }

        .benefit-card {
            background-color: var(--white);
            padding: 35px 30px;
            border-radius: 15px;
            box-shadow: var(--shadow);
            display: flex;
            align-items: flex-start;
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.03);
            height: 100%;
        }

        .benefit-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }

        .benefit-icon {
            font-size: 2.2rem;
            color: var(--primary-color);
            margin-right: 20px;
            flex-shrink: 0;
            background-color: rgba(52, 153, 104, 0.1);
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .benefit-content h3 {
            margin-bottom: 12px;
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--dark);
        }

        .benefit-content p {
            color: var(--gray);
            line-height: 1.6;
            font-size: 1rem;
        }

        .faq-section {
            padding: 100px 0;
            position: relative;
            overflow: hidden;
        }

        .faq-section::before {
            content: '';
            position: absolute;
            top: -150px;
            right: -150px;
            width: 300px;
            height: 300px;
            background-color: rgba(52, 153, 104, 0.05);
            border-radius: 50%;
            z-index: 0;
        }

        .faq-section::after {
            content: '';
            position: absolute;
            bottom: -150px;
            left: -150px;
            width: 300px;
            height: 300px;
            background-color: rgba(52, 153, 104, 0.05);
            border-radius: 50%;
            z-index: 0;
        }

        .faq-container {
            max-width: 900px;
            margin: 50px auto 0;
            position: relative;
            z-index: 1;
        }

        .faq-item {
            margin-bottom: 20px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow);
            border: 1px solid rgba(0,0,0,0.03);
            transition: all 0.3s ease;
        }

        .faq-item:hover {
            box-shadow: 0 10px 20px rgba(0,0,0,0.08);
        }

        .faq-question {
            background-color: var(--white);
            padding: 25px 30px;
            cursor: pointer;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: var(--dark);
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }

        .faq-question:hover {
            color: var(--primary-color);
        }

        .faq-question i {
            color: var(--primary-color);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .faq-question i.fa-chevron-up {
            transform: rotate(180deg);
        }

        .faq-answer {
            background-color: var(--white);
            border-top: 1px solid rgba(0,0,0,0.05);
            padding: 0 30px;
            max-height: 0;
            overflow: hidden;
            transition: all 0.5s ease;
        }

        .faq-answer.active {
            padding: 20px 30px 30px;
            max-height: 1000px;
        }

        .faq-answer p {
            color: var(--gray);
            line-height: 1.7;
            margin: 0;
        }

        @media (max-width: 1200px) {
            .pricing-grid {
                gap: 20px;
            }

            .pricing-content {
                padding: 30px 20px 25px;
            }

            .pricing-features li {
                font-size: 0.95rem;
            }
        }

        @media (max-width: 992px) {
            .page-header h1 {
                font-size: 2.5rem;
            }

            .page-header p {
                font-size: 1.1rem;
            }

            .pricing-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .pricing-card.popular {
                grid-column: 1 / -1;
                max-width: 600px;
                margin: 0 auto 20px;
            }

            .benefits-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .pricing-section h2, .benefits-section h2, .faq-section h2 {
                font-size: 2.2rem;
            }

            .section-header {
                font-size: 1.1rem;
            }

            .price-calculator {
                padding: 30px 25px;
                margin-top: 60px;
            }

            .price-calculator h3 {
                font-size: 1.6rem;
            }

            .calculator-field {
                min-width: 100%;
            }

            .calculator-form {
                gap: 20px;
            }
        }

        @media (max-width: 768px) {
            .page-header {
                padding-top: 120px;
                padding-bottom: 60px;
            }

            .page-header h1 {
                font-size: 2rem;
            }

            .page-header p {
                font-size: 1rem;
            }

            .pricing-section, .benefits-section, .faq-section {
                padding: 70px 0;
            }

            .pricing-grid {
                grid-template-columns: 1fr;
                max-width: 500px;
                margin-left: auto;
                margin-right: auto;
            }

            .pricing-card.popular {
                transform: scale(1);
                margin-top: 30px;
                margin-bottom: 30px;
            }

            .pricing-card.popular:hover {
                transform: translateY(-8px);
            }

            .benefits-grid {
                grid-template-columns: 1fr;
                max-width: 500px;
                margin-left: auto;
                margin-right: auto;
            }

            .benefit-card {
                flex-direction: column;
                text-align: center;
                padding: 30px 20px;
            }

            .benefit-icon {
                margin-right: 0;
                margin-bottom: 20px;
            }

            .pricing-section h2, .benefits-section h2, .faq-section h2 {
                font-size: 1.8rem;
            }

            .faq-question {
                padding: 20px;
                font-size: 1rem;
            }

            .faq-answer.active {
                padding: 15px 20px 20px;
            }
        }

        @media (max-width: 992px) {
            .pricing-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 25px;
            }

            .pricing-card:nth-child(3) {
                grid-column: span 2;
                max-width: 500px;
                margin: 0 auto;
            }
        }

        @media (max-width: 768px) {
            .pricing-grid, .benefits-grid {
                grid-template-columns: 1fr;
            }

            .pricing-card:nth-child(3) {
                grid-column: span 1;
            }

            .pricing-card.popular {
                transform: scale(1);
                margin: 30px 0;
                order: -1;
            }

            .pricing-card.popular:hover {
                transform: translateY(-8px);
            }

            .coffee-price-note {
                flex-direction: column;
                text-align: center;
                padding: 15px;
            }

            .coffee-icon {
                margin: 0 0 10px 0;
            }

            .page-header h1 {
                font-size: 1.8rem;
            }

            .pricing-header {
                padding: 25px 15px;
                border-radius: 15px 15px 0 0;
            }

            .pricing-header h3 {
                font-size: 1.1rem;
            }

            .pricing-large-text {
                font-size: 2rem;
            }

            .pricing-icon {
                width: 30px;
                height: 30px;
                line-height: 30px;
                font-size: 1rem;
            }

            .pricing-content {
                padding: 25px 20px;
            }

            .pricing-features li {
                padding-left: 30px;
                font-size: 0.95rem;
            }

            .pricing-features li:before {
                width: 20px;
                height: 20px;
                font-size: 0.7rem;
            }

            .price-calculator {
                padding: 30px 20px;
                margin: 60px auto 40px;
            }

            .calculator-field {
                min-width: 100%;
            }

            .price-calculator h3 {
                font-size: 1.4rem;
                margin-bottom: 25px;
            }

            .calculator-field label {
                font-size: 1rem;
            }

            .calculator-field input,
            .calculator-field select {
                padding: 12px 15px;
                font-size: 1rem;
            }

            .calculator-result {
                padding: 20px;
            }

            .result-value {
                font-size: 2.5rem;
            }

            .result-note {
                font-size: 0.9rem;
            }

            .popular-badge {
                top: 10px;
                right: 0;
                font-size: 0.75rem;
                padding: 4px 12px;
            }

            .benefit-icon {
                width: 50px;
                height: 50px;
                font-size: 1.8rem;
            }

            .benefit-content h3 {
                font-size: 1.2rem;
            }

            .benefit-content p {
                font-size: 0.95rem;
            }
        }

        @media (max-width: 480px) {
            .pricing-header {
                padding: 20px 15px;
            }

            .pricing-icon {
                width: 28px;
                height: 28px;
                line-height: 28px;
                font-size: 0.9rem;
            }

            .pricing-header h3 {
                font-size: 1rem;
            }

            .pricing-header .pricing-large-text {
                font-size: 1.8rem;
            }

            .pricing-content {
                padding: 20px 15px;
            }

            .pricing-subtitle {
                font-size: 1rem;
            }

            .pricing-features-title {
                font-size: 1rem;
            }

            .pricing-features li {
                font-size: 0.9rem;
                margin-bottom: 12px;
            }

            .pricing-cta .btn {
                padding: 12px 15px;
                font-size: 0.85rem;
            }

            .price-calculator h3 {
                font-size: 1.3rem;
            }

            .result-label {
                font-size: 1.1rem;
            }

            .result-value {
                font-size: 2.2rem;
            }

            .coffee-price-note {
                padding: 12px;
                border-radius: 15px;
            }

            .coffee-price-note p {
                font-size: 0.9rem;
            }

            .coffee-icon {
                width: 30px;
                height: 30px;
                font-size: 0.9rem;
            }

            .volume-discount {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.html" class="logo">
                    <img src="img/logo.png" alt="Growy NET logo">
                </a>
                <div class="menu-toggle" id="mobile-menu">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
                <ul class="nav-menu">
                    <li><a href="index.html" class="nav-link">Domů</a></li>
                    <li><a href="produkt.html" class="nav-link">Produkt</a></li>
                    <li><a href="moduly.html" class="nav-link">Moduly</a></li>
                    <li><a href="pro-koho.html" class="nav-link">Pro koho</a></li>
                    <li><a href="cena.html" class="nav-link active">Cena</a></li>
                    <li><a href="reference.html" class="nav-link">Reference</a></li>
                    <li><a href="blog.html" class="nav-link">Blog</a></li>
                    <li><a href="kontakt.html" class="nav-link">Kontakt</a></li>
                </ul>
                <a href="vyzkouset-zdarma.html" class="btn btn-primary">Vyzkoušet zdarma</a>
            </nav>
        </div>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1>Flexibilní plán pro každou firmu</h1>
            <p>Jste malá firma o pár zaměstnancích nebo mezinárodní skupina s tisíci zaměstnanci? Growy NET se Vám dokonale přizpůsobí s fér cenou dle počtu uživatelů a využívaných funkcí.</p>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="pricing-section">
        <div class="container">
            <h2>Cenové plány</h2>
            <p class="section-header">Vyberte si plán, který nejlépe odpovídá potřebám vaší firmy</p>




            <div class="pricing-grid">
                <div class="pricing-card">
                    <div class="pricing-header">
                        <div class="pricing-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h3>Start</h3>
                        <div class="pricing-large-text">od 49 Kč</div>
                        <span class="pricing-price-note">uživatel / měsíc</span>
                    </div>
                    <div class="pricing-content">
                        <div class="pricing-subtitle">Ideální pro malé a střední firmy</div>

                        <div>
                            <div class="pricing-features-title">Základní balíček obsahuje:</div>
                            <ul class="pricing-features">
                                <li><a href="moduly/kolegove.html">Kolegové - firemní adresář <i class="fas fa-arrow-right"></i></a></li>
                                <li><a href="moduly/chat.html">Chat - interní komunikace <i class="fas fa-arrow-right"></i></a></li>
                                <li><a href="moduly/novinky.html">Novinky - firemní aktuality <i class="fas fa-arrow-right"></i></a></li>
                                <li><a href="moduly/udalosti.html">Události - kalendář akcí <i class="fas fa-arrow-right"></i></a></li>
                                <li><a href="moduly/ankety.html">Ankety - zpětná vazba <i class="fas fa-arrow-right"></i></a></li>
                                <li><a href="moduly/dulezita-sdeleni.html">Důležitá sdělení <i class="fas fa-arrow-right"></i></a></li>
                                <li><a href="moduly/galerie.html">Galerie - sdílení fotografií <i class="fas fa-arrow-right"></i></a></li>
                                <li><a href="moduly/rozcestnik.html">Rozcestník - odkazy <i class="fas fa-arrow-right"></i></a></li>
                                <li><a href="moduly/faq.html">FAQ - často kladené dotazy <i class="fas fa-arrow-right"></i></a></li>
                                <li><a href="moduly/o-firme.html">O firmě - základní informace <i class="fas fa-arrow-right"></i></a></li>
                            </ul>
                        </div>

                        <div class="pricing-cta">
                            <a href="vyzkouset-zdarma.html" class="btn btn-primary">Vyzkoušet zdarma</a>
                        </div>
                    </div>
                </div>

                <div class="pricing-card popular">
                    <div class="popular-badge">Nejoblíbenější</div>
                    <div class="pricing-header">
                        <div class="pricing-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <h3>Plus</h3>
                        <div class="pricing-large-text">Individuální</div>
                        <span class="pricing-price-note">dle modulů</span>
                    </div>
                    <div class="pricing-content">
                        <div class="pricing-subtitle">Pro firmy s pokročilými potřebami</div>

                        <div>
                            <div class="pricing-features-title">Rozšiřující moduly:</div>
                            <ul class="pricing-features">
                                <li><a href="moduly/firemni-casopis.html">Firemní časopis - interní publikace <i class="fas fa-arrow-right"></i></a></li>
                                <li><a href="moduly/smernice.html">Směrnice - dokumenty a postupy <i class="fas fa-arrow-right"></i></a></li>
                                <li><a href="moduly/schranka-duvery.html">Schránka důvěry - anonymní zpětná vazba <i class="fas fa-arrow-right"></i></a></li>
                                <li><a href="moduly/zadosti.html">Žádosti - digitalizace firemních procesů <i class="fas fa-arrow-right"></i></a></li>
                                <li><a href="moduly/hlaseni-incidentu.html">Hlášení incidentů - BOZP a další <i class="fas fa-arrow-right"></i></a></li>
                                <li><a href="moduly/napady.html">Nápady a zlepšovací návrhy <i class="fas fa-arrow-right"></i></a></li>
                                <li><a href="moduly/onboarding.html">Onboarding - adaptace nových zaměstnanců <i class="fas fa-arrow-right"></i></a></li>
                                <li><a href="moduly/projekty.html">Projekty - správa a koordinace <i class="fas fa-arrow-right"></i></a></li>
                                <li>Všechny funkce z balíčku Start</li>
                            </ul>
                        </div>

                        <div class="pricing-cta">
                            <a href="vyzkouset-zdarma.html" class="btn btn-primary">Získat nabídku</a>
                        </div>
                    </div>
                </div>

                <div class="pricing-card">
                    <div class="pricing-header">
                        <div class="pricing-icon">
                            <i class="fas fa-crown"></i>
                        </div>
                        <h3>Enterprise</h3>
                        <div class="pricing-large-text">Na míru</div>
                        <span class="pricing-price-note">dle požadavků</span>
                    </div>
                    <div class="pricing-content">
                        <div class="pricing-subtitle">Pro velké společnosti se specifickými požadavky</div>

                        <div>
                            <div class="pricing-features-title">Prémiové služby:</div>
                            <ul class="pricing-features">
                                <li><a href="moduly/vyvoj-na-miru.html">Vývoj modulů na míru Vašim potřebám <i class="fas fa-arrow-right"></i></a></li>
                                <li><a href="moduly/integrace.html">Integrace s dalšími firemními systémy <i class="fas fa-arrow-right"></i></a></li>
                                <li><a href="moduly/vlastni-server.html">Možnost instalace na vlastní server <i class="fas fa-arrow-right"></i></a></li>
                                <li>Všechny funkce z balíčků Start a Plus</li>
                                <li><a href="moduly/prioritni-podpora.html">Prioritní zákaznická podpora <i class="fas fa-arrow-right"></i></a></li>
                                <li><a href="moduly/konzultant.html">Dedikovaný konzultant pro Vaši firmu <i class="fas fa-arrow-right"></i></a></li>
                                <li><a href="moduly/sla.html">Individuální SLA <i class="fas fa-arrow-right"></i></a></li>
                                <li><a href="moduly/konzultace-skoleni.html">Pravidelné konzultace a školení <i class="fas fa-arrow-right"></i></a></li>
                            </ul>
                        </div>

                        <div class="pricing-cta">
                            <a href="vyzkouset-zdarma.html" class="btn btn-primary">Kontaktovat nás</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Price Calculator -->
            <div class="price-calculator">
                <h3>Spočítejte si orientační cenu</h3>
                <div class="calculator-form">
                    <div class="calculator-field">
                        <label for="employees-count">Počet zaměstnanců:</label>
                        <input type="number" id="employees-count" min="1" value="10" placeholder="Zadejte počet zaměstnanců">
                    </div>
                    <div class="calculator-field">
                        <label for="plan-type">Typ plánu:</label>
                        <select id="plan-type">
                            <option value="start">Start (49 Kč/uživatel)</option>
                            <option value="plus">Plus (79 Kč/uživatel)</option>
                        </select>
                    </div>
                    <div class="calculator-result">
                        <div class="result-label">Orientační měsíční cena:</div>
                        <div class="result-value">490 Kč</div>
                        <div class="result-note">Cena je pouze orientační a může se lišit dle konkrétních požadavků. Pro přesnou kalkulaci nás kontaktujte.</div>

                        <div class="coffee-price-note">
                            <div class="coffee-icon">
                                <i class="fas fa-coffee"></i>
                            </div>
                            <p>Věděli jste, že základní tarif stojí jen <strong>49 Kč</strong> za uživatele měsíčně? To je méně než cena jednoho šálku kávy, a přitom získáte plnohodnotnou firemní aplikaci!</p>
                        </div>

                        <div class="volume-discount">
                            <i class="fas fa-percentage"></i> Při větším počtu uživatelů automaticky získáváte množstevní slevu!
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Benefits Section -->
    <section class="benefits-section">
        <div class="container">
            <h2>Dále od nás získáte</h2>

            <div class="benefits-grid">
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <div class="benefit-content">
                        <h3>Úvodní konzultaci</h3>
                        <p>Před zahájením spolupráce s Vámi probereme Vaše potřeby a navrhneme optimální řešení pro Vaši firmu.</p>
                    </div>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-vial"></i>
                    </div>
                    <div class="benefit-content">
                        <h3>Zkušební verzi aplikace</h3>
                        <p>Poskytneme Vám zkušební verzi aplikace, abyste si mohli vyzkoušet všechny funkce a ověřit, že Growy NET je pro Vás to pravé.</p>
                    </div>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <div class="benefit-content">
                        <h3>Pomoc se zavedením systému ve firmě</h3>
                        <p>Náš tým Vám pomůže s implementací Growy NET ve Vaší firmě, včetně nastavení, importu dat a zaškolení administrátorů.</p>
                    </div>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="benefit-content">
                        <h3>Zákaznickou podporu</h3>
                        <p>Poskytujeme zákaznickou podporu každý pracovní den od 8:00 do 16:00, abychom Vám pomohli s jakýmkoliv problémem.</p>
                    </div>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="benefit-content">
                        <h3>Zálohování dat aplikace</h3>
                        <p>Pravidelně zálohujeme všechna data, abychom zajistili jejich bezpečnost a dostupnost v případě potřeby.</p>
                    </div>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="benefit-content">
                        <h3>Pravidelné aktualizace</h3>
                        <p>Neustále pracujeme na vylepšování aplikace a přidávání nových funkcí, které automaticky získáváte v rámci předplatného.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section">
        <div class="container">
            <h2>Často kladené dotazy</h2>

            <div class="faq-container">
                <div class="faq-item">
                    <div class="faq-question">
                        Jak zjistím přesnou cenu za uživatele?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Kontaktujte nás a obratem se Vám ozve náš obchodní zástupce, který Vám kromě cenových podmínek nabídne také zkušební verzi aplikace zdarma.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        Je možné měnit počet uživatelů dle aktuálního stavu zaměstnanců?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Ano, uživatele je možné flexibilně měnit dle aktuálního stavu zaměstnanců. Počet uživatelů Vám kdykoliv snížíme/navýšíme na základě žádosti. Na základě změny dojde také k úpravě výše měsíčního předplatného dle aktuálního počtu uživatelů a ceníku.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        Jaký je rozdíl mezi variantami Start, Plus a Enterprise?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Growy NET můžete využívat v základní instalaci (= varianta Start), která je zpoplatněná na základě počtu uživatelů, tedy počtu zaměstnanců, kteří budou aplikaci využívat. V případě zájmu je možné k základní instalaci přikoupit také speciální moduly z balíčku Plus či vyvinout vlastní modul na míru v rámci balíčku enterprise.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        Mohu si v rámci zkušební verze vyzkoušet veškerou funkcionalitu?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Ano, během zkušební verze Vám automaticky zapneme všechny funkce z varianty Start i Plus.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        Je možné využívat pouze měsíční předplatné?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Ano, aktuálně nabízíme provoz aplikace pouze na základě měsíčního předplatného.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        Jaké platební metody přijímáte?
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Aktuálně přijímáme pouze platbu bankovním převodem na základě vystavené faktury k měsíčnímu předplatnému.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
        <div class="container">
            <h2>Zaujalo Vás naše řešení?</h2>
            <p>Kontaktujte nás a vyzkoušejte aplikaci na vlastní kůži.</p>
            <a href="vyzkouset-zdarma.html" class="btn btn-primary">Vyzkoušet zdarma</a>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-top">
                <div class="footer-logo">
                    <img src="img/logo.png" alt="Growy NET logo">
                    <p>Flexibilní zaměstnanecká aplikace</p>
                    <div class="social-links">
                        <a href="https://www.linkedin.com/company/growy-net" target="_blank"><i class="fab fa-linkedin"></i></a>
                        <a href="https://www.facebook.com/growynet.cz/" target="_blank"><i class="fab fa-facebook"></i></a>
                        <a href="https://www.instagram.com/growy_net/" target="_blank"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h3>Stránky</h3>
                    <ul>
                        <li><a href="produkt.html">Produkt</a></li>
                        <li><a href="moduly.html">Moduly</a></li>
                        <li><a href="cena.html">Cena</a></li>
                        <li><a href="kontakt.html">Kontakt</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Kontakt</h3>
                    <p><strong>growy s.r.o.</strong></p>
                    <p>Na Folimance 2155/15, Praha, 120 00</p>
                    <p>+420 702 040 289, <EMAIL></p>
                    <p>IČ: 09477454, DIČ: CZ09477454</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>Copyright © 2025 Growy NET | Powered by Growy</p>
                <div class="footer-legal">
                    <a href="podminky-uzivani.html">Podmínky užívání</a>
                    <a href="zasady-zpracovani-osobnich-udaju.html">Zásady zpracování osobních údajů</a>
                    <a href="zasady-cookies.html">Zásady cookies (EU)</a>
                </div>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script src="js/animations.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // FAQ Toggle
            const faqQuestions = document.querySelectorAll('.faq-question');

            faqQuestions.forEach(question => {
                question.addEventListener('click', () => {
                    const answer = question.nextElementSibling;
                    const icon = question.querySelector('i');

                    // Close all other answers
                    document.querySelectorAll('.faq-answer').forEach(item => {
                        if (item !== answer) {
                            item.classList.remove('active');
                        }
                    });

                    // Reset all icons
                    document.querySelectorAll('.faq-question i').forEach(i => {
                        if (i !== icon) {
                            i.classList.remove('fa-chevron-up');
                            i.classList.add('fa-chevron-down');
                        }
                    });

                    // Toggle current answer
                    answer.classList.toggle('active');
                    icon.classList.toggle('fa-chevron-up');
                    icon.classList.toggle('fa-chevron-down');
                });
            });

            // Price Calculator
            const employeesInput = document.getElementById('employees-count');
            const planSelect = document.getElementById('plan-type');
            const resultValue = document.querySelector('.result-value');

            // Base prices
            const prices = {
                start: 49,
                plus: 79
            };

            // Calculate and update price
            function updatePrice() {
                const employees = parseInt(employeesInput.value) || 0;
                const plan = planSelect.value;
                const basePrice = prices[plan];

                // Calculate total price
                let totalPrice = employees * basePrice;

                // Apply volume discount
                if (employees >= 100) {
                    totalPrice = totalPrice * 0.9; // 10% discount for 100+ employees
                } else if (employees >= 50) {
                    totalPrice = totalPrice * 0.95; // 5% discount for 50+ employees
                }

                // Format and display price
                resultValue.textContent = Math.round(totalPrice) + ' Kč';
            }

            // Add event listeners
            employeesInput.addEventListener('input', updatePrice);
            planSelect.addEventListener('change', updatePrice);

            // Initialize price
            updatePrice();
        });
    </script>
</body>
</html>
